-- Merging decision tree log ---
manifest
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:1:1-32:12
MERGED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:1:1-32:12
INJECTED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:react-native-google-signin_google-signin] D:\dev\Zayton\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] D:\dev\Zayton\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] D:\dev\Zayton\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] D:\dev\Zayton\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-edge-to-edge] D:\dev\Zayton\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] D:\dev\Zayton\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-firebase_auth] D:\dev\Zayton\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-11:12
MERGED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-30:12
MERGED from [:expo] D:\dev\Zayton\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] D:\dev\Zayton\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] D:\dev\Zayton\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-32:12
MERGED from [:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-24:12
MERGED from [:expo-dev-menu-interface] D:\dev\Zayton\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-18:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\343b02feb901b99c902418086926019a\transformed\expo.modules.font-13.3.2\AndroidManifest.xml:2:1-7:12
MERGED from [BareExpo:expo.modules.image:2.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e48a11f88c3f45b6a25ca3a530edcfb2\transformed\expo.modules.image-2.3.2\AndroidManifest.xml:2:1-19:12
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\d47d38993ab055da05a5bcb9a87031cc\transformed\expo.modules.splashscreen-0.30.10\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d99ddd4c7e8471c63b599024a6bbf23\transformed\expo.modules.systemui-5.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c8639f7c924b1c08482d0e5be29b1\transformed\react-android-0.79.5-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b71d042e5591977e658555b7410cc83\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29acdc0321d00a6f785c8c7eac64d289\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\c06210739c1eceecdcd152c368ed657c\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f625bf36cee328d33872427d130297f5\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1b9adfe2fe40bcb01931d09ffc344cd\transformed\glide-plugin-3.0.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\893008ec870457b70bbe0cd1702851ae\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa6e11fc96ef180b67100b8351118a75\transformed\awebp-3.0.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\20ea7621943f2aa10c1b9a5aff285dfb\transformed\apng-3.0.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\24576e7169bce45bc96583ae5c4c7691\transformed\gif-3.0.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a603f9d58905849d631d29d6c22c3ff8\transformed\avif-3.0.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1b858b88db50336b5cfcaebe792939c\transformed\frameanimation-3.0.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aeb9e9dd35f43750000a4cf9482a6b3a\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a31caba1ca6c7efb87776a8eff37748\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\227220ca1e9af71d8f911d6a56507836\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1103ded6ce5448a25451628abcf8d567\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88e392acb9b1e3fdf9afaa14c9a10708\transformed\play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ce214f64706bbaa1149d5e3a6c3d23\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a030daa6476f697c81995d7b6ab8706b\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70cc3ca3259d8dac5931c14081011531\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79b7baaf501e70367516e3b80eb83462\transformed\recaptcha-18.6.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cece674fe65fde6603de176391bdcac\transformed\integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70e28d8c4211b2d951cb4920c8bf4e64\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d58f2034c98c4c83eb3d75de56efc6c\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\99fd00b38b58e7ac445ebca5871df660\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f371e84ced55de24cfa5793c07b48c02\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:2:1-33:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f4a06144a561c42efa28559f86d477\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f9040ab91857bd9322e2d28e05ad582\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\982c199d7e2700da77e4269f80bb4b0e\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e16c3202f3cd81e57fb00d89195176fc\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\179ad6747de8fa2d417ca25007d47b0c\transformed\webkit-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3edfeb6409ab14b610eed170bc79f0c5\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa6baf461cfd7d2bb9b08158e97670e8\transformed\animated-gif-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f344b5bc7a25549eaead406693e84b07\transformed\webpsupport-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4166342b87604398fe6fb872a960ec7f\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827f2accfd9c62987319144680af3551\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1492ee24e4eedf689436e1360f0e852b\transformed\animated-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06d0df9a25d761e9e0551c3b9212b270\transformed\animated-drawable-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f0ae0df3c3350d9813dae9e6014abe4\transformed\vito-options-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\186084dcbdbf9dfe5355b8b16740faf1\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\37d86171d00cfbde155c92f7bb22511c\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d5b70b9ef220ce88a02c00f7fc8639c\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f84ce999ab337fa8ad445e70d009f5e0\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca2e054104fa41176d44d2e6cd9c2501\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3796d36fa7886ff83798961c8f00938\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea662a4f759474251ed17355a2cdbc6e\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbb617ee1244aa026d445cb33828903e\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab458c5a509fd7fc448d1cdd9b70caf4\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bd80274745581635bc84535beb5ebb6\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a008fa8b81010d20c4924d2dac80384\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0169c5fe1140951411594d49eaea24f9\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af23ba37a4577692640f0cb8505defa\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\332d023433322325f0b2705b83ef143a\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e0f931cca7b5c832001cb00ac7885c5\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a82ba172b7765fe7f88756fcb58d5cb6\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3634438f086cc50a9c481bcfaa76ca54\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcf4d7895820478434dcfcb1886e33b5\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc51cbfcdb203b33ac78aba8c46d7cba\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d65d6ca76628701ccdf0d352ae5800e\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:2:1-15:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7979c5b569631585b896f06112c70bce\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53710fd81a89ecbc6ff7a87a427c2855\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\580f980e805cebadc521afbd1dd44bce\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d9694e94f0970a98626ab0901689787\transformed\avif-integration-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2d822b6c90f4756b2131804935affeb\transformed\glide-transformations-4.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\720b5c07abf3dc94b013128bf7fdc5ec\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:2:1-16:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\676a37d587cdd5ac0aff90c249419f36\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf6fe1cbbd3da87e397869524987e974\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5047b06a0a16afa6b40ec1ac1fc94425\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2d58a01f58abceb465e9bccede9a91e\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f38a89604ec213e47f3b07db247babba\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d563bf73ea2f5e14f513ee4a2a03c71a\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88d8b97f7937ab9ef760e5eb5da0ac6d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd258ec638923d5d9af6e8166846101c\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d35e13404ce9a1f9f63c96c0876c9b8\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\788143b298f37c442d7e4a78532ab930\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f288675642bbb5e7c93c8d8c296eb07\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a94099a95920b7409bb6e184454f9838\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d3f62a34a11dbb0c091c6ec36a5284f\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c204666627139d9b653d7e82bc97180\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\34cb148022c62c6a1f631dbc344ed814\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4688fae6cbe4dac0f7e1deff2257c956\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\be3c89fcba0c21a2d59283ff757249fb\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78a3ac46ecd2f8f09158d327335f50a9\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\18fea40f699716ece17692486d5596d3\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0f34f31b23f8028b4e3d445ac775901\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b21e230afe76ee550c22d5fdbf96371\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e65b91116e4a2b0c02b989327e2673e\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29feacd8c05b83c8cba26658c0ed7080\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7740120f109f93963bab9b03c123192f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ff9a80af4b2daedf3b752906498527b\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b791ede67e0543577c3834b00abe3\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [:expo-constants] D:\dev\Zayton\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-client] D:\dev\Zayton\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-manifests] D:\dev\Zayton\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] D:\dev\Zayton\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] D:\dev\Zayton\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\8911228e062f3aaa56a40127207a20b5\transformed\expo.modules.asset-11.1.7\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\42956b53b061f7a1af21ed506d376ce4\transformed\expo.modules.blur-14.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6db0bb632162c63496e487b980959fc0\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:2:1-9:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a05dbb743b729afe73f923e69d2426a6\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\49b7e53260c404a9eb0a2f3daf7f56e6\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.linking:7.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\710f7d6533e949dec100aa23916a347b\transformed\expo.modules.linking-7.1.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa27b8ae0105f42da4d02e6036fc2172\transformed\googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3ab7309334398ea0977c8a6079a31e0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7a56ab820fc9fdf0a911787f27fccf7\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5819168c9856fbf46eb00ae8c5aaa08d\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f71c7b7206e7973f01bc15e0cb61b11a\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c312c62d44405731adb5ff9493a546e7\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26c0a5185c80c2eff4ff2c07430bf1bc\transformed\vito-renderer-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc762884eab1ccc882e88c8a2a07adb5\transformed\hermes-android-0.79.5-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9dc2d75673b1ddc5f92823da9ee0d3e4\transformed\viewbinding-8.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\14571843b74eba9256df3cf83913af6e\transformed\BlurView-version-2.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\135027b9f17a553df1ae07f8191e0e02\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d64d971eb9486b94b61cb4a12a37b722\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4fb2a2b87176ed3a593f3493ec69a7c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ec5bda675fb0123fca261876e7e2609\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26c3b200750d3ba1b9e51cf82e0423ee\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba02d1b07cbf5634c1e8c785edfe4930\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14d1256b203a661e073c238988e62135\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1606e9aded3459c6ed9cfc6e8bbc9f8\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e6899ecdb43c673ff71e1a8dde09cb\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5adea9e76276a7a7aa263dfa62ba5b32\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5338620589e3d5ecd2308e61ee9b273f\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9970e86d3d8ec320aeeff3c5deadaad5\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79a6adbe0dc8a96797476d070ae79d0e\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e66072023c19c861b98fb96aa058203\transformed\androidsvg-aar-1.4\AndroidManifest.xml:2:1-11:12
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af7c7096ec59499e3543e60849ae376\transformed\avif-1.1.1.14d8e3c4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:2:3-64
MERGED from [:react-native-firebase_auth] D:\dev\Zayton\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_auth] D:\dev\Zayton\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [BareExpo:expo.modules.image:2.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e48a11f88c3f45b6a25ca3a530edcfb2\transformed\expo.modules.image-2.3.2\AndroidManifest.xml:7:5-67
MERGED from [BareExpo:expo.modules.image:2.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e48a11f88c3f45b6a25ca3a530edcfb2\transformed\expo.modules.image-2.3.2\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79b7baaf501e70367516e3b80eb83462\transformed\recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79b7baaf501e70367516e3b80eb83462\transformed\recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:8:5-67
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:8:5-67
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:2:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:3:3-77
MERGED from [BareExpo:expo.modules.image:2.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e48a11f88c3f45b6a25ca3a530edcfb2\transformed\expo.modules.image-2.3.2\AndroidManifest.xml:15:5-17:38
MERGED from [BareExpo:expo.modules.image:2.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e48a11f88c3f45b6a25ca3a530edcfb2\transformed\expo.modules.image-2.3.2\AndroidManifest.xml:15:5-17:38
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:10:5-80
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:10:5-80
	android:maxSdkVersion
		ADDED from [BareExpo:expo.modules.image:2.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e48a11f88c3f45b6a25ca3a530edcfb2\transformed\expo.modules.image-2.3.2\AndroidManifest.xml:17:9-35
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:3:20-75
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:4:3-75
MERGED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:4:3-75
MERGED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:4:3-75
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c8639f7c924b1c08482d0e5be29b1\transformed\react-android-0.79.5-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c8639f7c924b1c08482d0e5be29b1\transformed\react-android-0.79.5-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:4:20-73
uses-permission#android.permission.VIBRATE
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:5:3-63
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6db0bb632162c63496e487b980959fc0\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6db0bb632162c63496e487b980959fc0\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:5:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:6:3-78
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:9:5-81
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:9:5-81
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:6:20-76
queries
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:7:3-13:13
MERGED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
MERGED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d65d6ca76628701ccdf0d352ae5800e\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:7:5-13:15
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d65d6ca76628701ccdf0d352ae5800e\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:7:5-13:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:8:5-12:14
action#android.intent.action.VIEW
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:9:7-58
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:9:15-56
category#android.intent.category.BROWSABLE
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:10:7-67
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:10:17-65
data
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:7-37
	android:scheme
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:13-35
application
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:3-31:17
MERGED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:3-31:17
MERGED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:3-31:17
INJECTED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml:6:5-162
MERGED from [:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-28:19
MERGED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-28:19
MERGED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-30:19
MERGED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-30:19
MERGED from [:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-22:19
MERGED from [:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-22:19
MERGED from [:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c8639f7c924b1c08482d0e5be29b1\transformed\react-android-0.79.5-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c8639f7c924b1c08482d0e5be29b1\transformed\react-android-0.79.5-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29acdc0321d00a6f785c8c7eac64d289\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29acdc0321d00a6f785c8c7eac64d289\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\893008ec870457b70bbe0cd1702851ae\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\893008ec870457b70bbe0cd1702851ae\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1103ded6ce5448a25451628abcf8d567\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1103ded6ce5448a25451628abcf8d567\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88e392acb9b1e3fdf9afaa14c9a10708\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88e392acb9b1e3fdf9afaa14c9a10708\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ce214f64706bbaa1149d5e3a6c3d23\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ce214f64706bbaa1149d5e3a6c3d23\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a030daa6476f697c81995d7b6ab8706b\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a030daa6476f697c81995d7b6ab8706b\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70cc3ca3259d8dac5931c14081011531\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70cc3ca3259d8dac5931c14081011531\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cece674fe65fde6603de176391bdcac\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cece674fe65fde6603de176391bdcac\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70e28d8c4211b2d951cb4920c8bf4e64\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70e28d8c4211b2d951cb4920c8bf4e64\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:20:5-31:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:20:5-31:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\720b5c07abf3dc94b013128bf7fdc5ec\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:10:5-14:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\720b5c07abf3dc94b013128bf7fdc5ec\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:10:5-14:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4688fae6cbe4dac0f7e1deff2257c956\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4688fae6cbe4dac0f7e1deff2257c956\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29feacd8c05b83c8cba26658c0ed7080\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29feacd8c05b83c8cba26658c0ed7080\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7740120f109f93963bab9b03c123192f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7740120f109f93963bab9b03c123192f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3ab7309334398ea0977c8a6079a31e0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3ab7309334398ea0977c8a6079a31e0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\14571843b74eba9256df3cf83913af6e\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\14571843b74eba9256df3cf83913af6e\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba02d1b07cbf5634c1e8c785edfe4930\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba02d1b07cbf5634c1e8c785edfe4930\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79a6adbe0dc8a96797476d070ae79d0e\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79a6adbe0dc8a96797476d070ae79d0e\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af7c7096ec59499e3543e60849ae376\transformed\avif-1.1.1.14d8e3c4\AndroidManifest.xml:5:5-6:19
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af7c7096ec59499e3543e60849ae376\transformed\avif-1.1.1.14d8e3c4\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:221-247
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:221-247
	android:label
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:48-80
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:48-80
	tools:ignore
		ADDED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:116-161
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:116-161
	tools:targetApi
		ADDED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml:6:54-74
	android:icon
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:81-115
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:81-115
	android:allowBackup
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:162-188
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:162-188
	android:theme
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:189-220
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:189-220
	tools:replace
		ADDED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml:6:18-53
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:16-47
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:16-47
meta-data#expo.modules.updates.ENABLED
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:15:5-83
	android:value
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:15:60-81
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:15:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:16:5-105
	android:value
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:16:81-103
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:16:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:17:5-99
	android:value
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:17:80-97
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:17:16-79
activity#com.omaraglan96.zayton.MainActivity
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:5-30:16
	android:screenOrientation
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:280-316
	android:launchMode
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:135-166
	android:windowSoftInputMode
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:167-209
	android:exported
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:256-279
	android:configChanges
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:44-134
	android:theme
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:210-255
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:19:7-22:23
action#android.intent.action.MAIN
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:20:9-60
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:20:17-58
category#android.intent.category.LAUNCHER
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:21:9-68
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:21:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:exp+zayton+data:scheme:zayton
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:23:7-29:23
category#android.intent.category.DEFAULT
ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:25:9-67
	android:name
		ADDED from D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:25:19-65
uses-sdk
INJECTED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml
MERGED from [:react-native-google-signin_google-signin] D:\dev\Zayton\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-google-signin_google-signin] D:\dev\Zayton\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] D:\dev\Zayton\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] D:\dev\Zayton\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\dev\Zayton\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\dev\Zayton\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\dev\Zayton\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\dev\Zayton\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] D:\dev\Zayton\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] D:\dev\Zayton\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] D:\dev\Zayton\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] D:\dev\Zayton\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_auth] D:\dev\Zayton\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_auth] D:\dev\Zayton\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo] D:\dev\Zayton\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] D:\dev\Zayton\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] D:\dev\Zayton\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] D:\dev\Zayton\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] D:\dev\Zayton\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] D:\dev\Zayton\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] D:\dev\Zayton\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] D:\dev\Zayton\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\343b02feb901b99c902418086926019a\transformed\expo.modules.font-13.3.2\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\343b02feb901b99c902418086926019a\transformed\expo.modules.font-13.3.2\AndroidManifest.xml:5:5-44
MERGED from [BareExpo:expo.modules.image:2.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e48a11f88c3f45b6a25ca3a530edcfb2\transformed\expo.modules.image-2.3.2\AndroidManifest.xml:4:5-44
MERGED from [BareExpo:expo.modules.image:2.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e48a11f88c3f45b6a25ca3a530edcfb2\transformed\expo.modules.image-2.3.2\AndroidManifest.xml:4:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\d47d38993ab055da05a5bcb9a87031cc\transformed\expo.modules.splashscreen-0.30.10\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\d47d38993ab055da05a5bcb9a87031cc\transformed\expo.modules.splashscreen-0.30.10\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d99ddd4c7e8471c63b599024a6bbf23\transformed\expo.modules.systemui-5.0.10\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d99ddd4c7e8471c63b599024a6bbf23\transformed\expo.modules.systemui-5.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c8639f7c924b1c08482d0e5be29b1\transformed\react-android-0.79.5-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c8639f7c924b1c08482d0e5be29b1\transformed\react-android-0.79.5-debug\AndroidManifest.xml:10:5-44
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b71d042e5591977e658555b7410cc83\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b71d042e5591977e658555b7410cc83\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29acdc0321d00a6f785c8c7eac64d289\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29acdc0321d00a6f785c8c7eac64d289\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\c06210739c1eceecdcd152c368ed657c\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\c06210739c1eceecdcd152c368ed657c\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f625bf36cee328d33872427d130297f5\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f625bf36cee328d33872427d130297f5\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1b9adfe2fe40bcb01931d09ffc344cd\transformed\glide-plugin-3.0.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1b9adfe2fe40bcb01931d09ffc344cd\transformed\glide-plugin-3.0.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\893008ec870457b70bbe0cd1702851ae\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\893008ec870457b70bbe0cd1702851ae\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa6e11fc96ef180b67100b8351118a75\transformed\awebp-3.0.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa6e11fc96ef180b67100b8351118a75\transformed\awebp-3.0.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\20ea7621943f2aa10c1b9a5aff285dfb\transformed\apng-3.0.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\20ea7621943f2aa10c1b9a5aff285dfb\transformed\apng-3.0.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\24576e7169bce45bc96583ae5c4c7691\transformed\gif-3.0.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\24576e7169bce45bc96583ae5c4c7691\transformed\gif-3.0.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a603f9d58905849d631d29d6c22c3ff8\transformed\avif-3.0.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a603f9d58905849d631d29d6c22c3ff8\transformed\avif-3.0.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1b858b88db50336b5cfcaebe792939c\transformed\frameanimation-3.0.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1b858b88db50336b5cfcaebe792939c\transformed\frameanimation-3.0.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aeb9e9dd35f43750000a4cf9482a6b3a\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aeb9e9dd35f43750000a4cf9482a6b3a\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a31caba1ca6c7efb87776a8eff37748\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a31caba1ca6c7efb87776a8eff37748\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\227220ca1e9af71d8f911d6a56507836\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\227220ca1e9af71d8f911d6a56507836\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1103ded6ce5448a25451628abcf8d567\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1103ded6ce5448a25451628abcf8d567\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88e392acb9b1e3fdf9afaa14c9a10708\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88e392acb9b1e3fdf9afaa14c9a10708\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ce214f64706bbaa1149d5e3a6c3d23\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ce214f64706bbaa1149d5e3a6c3d23\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a030daa6476f697c81995d7b6ab8706b\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a030daa6476f697c81995d7b6ab8706b\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70cc3ca3259d8dac5931c14081011531\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70cc3ca3259d8dac5931c14081011531\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79b7baaf501e70367516e3b80eb83462\transformed\recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79b7baaf501e70367516e3b80eb83462\transformed\recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cece674fe65fde6603de176391bdcac\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cece674fe65fde6603de176391bdcac\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70e28d8c4211b2d951cb4920c8bf4e64\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70e28d8c4211b2d951cb4920c8bf4e64\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d58f2034c98c4c83eb3d75de56efc6c\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d58f2034c98c4c83eb3d75de56efc6c\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\99fd00b38b58e7ac445ebca5871df660\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\99fd00b38b58e7ac445ebca5871df660\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f371e84ced55de24cfa5793c07b48c02\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f371e84ced55de24cfa5793c07b48c02\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:6:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f4a06144a561c42efa28559f86d477\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f4a06144a561c42efa28559f86d477\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f9040ab91857bd9322e2d28e05ad582\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f9040ab91857bd9322e2d28e05ad582\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\982c199d7e2700da77e4269f80bb4b0e\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\982c199d7e2700da77e4269f80bb4b0e\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e16c3202f3cd81e57fb00d89195176fc\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e16c3202f3cd81e57fb00d89195176fc\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\179ad6747de8fa2d417ca25007d47b0c\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\179ad6747de8fa2d417ca25007d47b0c\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3edfeb6409ab14b610eed170bc79f0c5\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3edfeb6409ab14b610eed170bc79f0c5\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa6baf461cfd7d2bb9b08158e97670e8\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa6baf461cfd7d2bb9b08158e97670e8\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f344b5bc7a25549eaead406693e84b07\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f344b5bc7a25549eaead406693e84b07\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4166342b87604398fe6fb872a960ec7f\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4166342b87604398fe6fb872a960ec7f\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827f2accfd9c62987319144680af3551\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827f2accfd9c62987319144680af3551\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1492ee24e4eedf689436e1360f0e852b\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1492ee24e4eedf689436e1360f0e852b\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06d0df9a25d761e9e0551c3b9212b270\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06d0df9a25d761e9e0551c3b9212b270\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f0ae0df3c3350d9813dae9e6014abe4\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f0ae0df3c3350d9813dae9e6014abe4\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\186084dcbdbf9dfe5355b8b16740faf1\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\186084dcbdbf9dfe5355b8b16740faf1\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\37d86171d00cfbde155c92f7bb22511c\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\37d86171d00cfbde155c92f7bb22511c\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d5b70b9ef220ce88a02c00f7fc8639c\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d5b70b9ef220ce88a02c00f7fc8639c\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f84ce999ab337fa8ad445e70d009f5e0\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f84ce999ab337fa8ad445e70d009f5e0\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca2e054104fa41176d44d2e6cd9c2501\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca2e054104fa41176d44d2e6cd9c2501\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3796d36fa7886ff83798961c8f00938\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3796d36fa7886ff83798961c8f00938\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea662a4f759474251ed17355a2cdbc6e\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea662a4f759474251ed17355a2cdbc6e\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbb617ee1244aa026d445cb33828903e\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbb617ee1244aa026d445cb33828903e\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab458c5a509fd7fc448d1cdd9b70caf4\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab458c5a509fd7fc448d1cdd9b70caf4\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bd80274745581635bc84535beb5ebb6\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bd80274745581635bc84535beb5ebb6\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a008fa8b81010d20c4924d2dac80384\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a008fa8b81010d20c4924d2dac80384\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0169c5fe1140951411594d49eaea24f9\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0169c5fe1140951411594d49eaea24f9\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af23ba37a4577692640f0cb8505defa\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af23ba37a4577692640f0cb8505defa\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\332d023433322325f0b2705b83ef143a\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\332d023433322325f0b2705b83ef143a\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e0f931cca7b5c832001cb00ac7885c5\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e0f931cca7b5c832001cb00ac7885c5\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a82ba172b7765fe7f88756fcb58d5cb6\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a82ba172b7765fe7f88756fcb58d5cb6\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3634438f086cc50a9c481bcfaa76ca54\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3634438f086cc50a9c481bcfaa76ca54\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcf4d7895820478434dcfcb1886e33b5\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcf4d7895820478434dcfcb1886e33b5\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc51cbfcdb203b33ac78aba8c46d7cba\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc51cbfcdb203b33ac78aba8c46d7cba\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d65d6ca76628701ccdf0d352ae5800e\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d65d6ca76628701ccdf0d352ae5800e\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7979c5b569631585b896f06112c70bce\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7979c5b569631585b896f06112c70bce\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53710fd81a89ecbc6ff7a87a427c2855\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53710fd81a89ecbc6ff7a87a427c2855\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\580f980e805cebadc521afbd1dd44bce\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\580f980e805cebadc521afbd1dd44bce\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d9694e94f0970a98626ab0901689787\transformed\avif-integration-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d9694e94f0970a98626ab0901689787\transformed\avif-integration-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2d822b6c90f4756b2131804935affeb\transformed\glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2d822b6c90f4756b2131804935affeb\transformed\glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\720b5c07abf3dc94b013128bf7fdc5ec\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\720b5c07abf3dc94b013128bf7fdc5ec\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\676a37d587cdd5ac0aff90c249419f36\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\676a37d587cdd5ac0aff90c249419f36\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf6fe1cbbd3da87e397869524987e974\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf6fe1cbbd3da87e397869524987e974\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5047b06a0a16afa6b40ec1ac1fc94425\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5047b06a0a16afa6b40ec1ac1fc94425\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2d58a01f58abceb465e9bccede9a91e\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2d58a01f58abceb465e9bccede9a91e\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f38a89604ec213e47f3b07db247babba\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f38a89604ec213e47f3b07db247babba\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d563bf73ea2f5e14f513ee4a2a03c71a\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d563bf73ea2f5e14f513ee4a2a03c71a\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88d8b97f7937ab9ef760e5eb5da0ac6d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88d8b97f7937ab9ef760e5eb5da0ac6d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd258ec638923d5d9af6e8166846101c\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd258ec638923d5d9af6e8166846101c\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d35e13404ce9a1f9f63c96c0876c9b8\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d35e13404ce9a1f9f63c96c0876c9b8\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\788143b298f37c442d7e4a78532ab930\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\788143b298f37c442d7e4a78532ab930\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f288675642bbb5e7c93c8d8c296eb07\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f288675642bbb5e7c93c8d8c296eb07\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a94099a95920b7409bb6e184454f9838\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a94099a95920b7409bb6e184454f9838\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d3f62a34a11dbb0c091c6ec36a5284f\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d3f62a34a11dbb0c091c6ec36a5284f\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c204666627139d9b653d7e82bc97180\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c204666627139d9b653d7e82bc97180\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\34cb148022c62c6a1f631dbc344ed814\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\34cb148022c62c6a1f631dbc344ed814\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4688fae6cbe4dac0f7e1deff2257c956\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4688fae6cbe4dac0f7e1deff2257c956\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\be3c89fcba0c21a2d59283ff757249fb\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\be3c89fcba0c21a2d59283ff757249fb\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78a3ac46ecd2f8f09158d327335f50a9\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\78a3ac46ecd2f8f09158d327335f50a9\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\18fea40f699716ece17692486d5596d3\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\18fea40f699716ece17692486d5596d3\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0f34f31b23f8028b4e3d445ac775901\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0f34f31b23f8028b4e3d445ac775901\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b21e230afe76ee550c22d5fdbf96371\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b21e230afe76ee550c22d5fdbf96371\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e65b91116e4a2b0c02b989327e2673e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e65b91116e4a2b0c02b989327e2673e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29feacd8c05b83c8cba26658c0ed7080\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29feacd8c05b83c8cba26658c0ed7080\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7740120f109f93963bab9b03c123192f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7740120f109f93963bab9b03c123192f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ff9a80af4b2daedf3b752906498527b\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ff9a80af4b2daedf3b752906498527b\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b791ede67e0543577c3834b00abe3\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d9b791ede67e0543577c3834b00abe3\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [:expo-constants] D:\dev\Zayton\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] D:\dev\Zayton\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] D:\dev\Zayton\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] D:\dev\Zayton\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] D:\dev\Zayton\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] D:\dev\Zayton\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] D:\dev\Zayton\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] D:\dev\Zayton\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] D:\dev\Zayton\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] D:\dev\Zayton\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\8911228e062f3aaa56a40127207a20b5\transformed\expo.modules.asset-11.1.7\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\8911228e062f3aaa56a40127207a20b5\transformed\expo.modules.asset-11.1.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\42956b53b061f7a1af21ed506d376ce4\transformed\expo.modules.blur-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\42956b53b061f7a1af21ed506d376ce4\transformed\expo.modules.blur-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6db0bb632162c63496e487b980959fc0\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6db0bb632162c63496e487b980959fc0\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a05dbb743b729afe73f923e69d2426a6\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a05dbb743b729afe73f923e69d2426a6\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\49b7e53260c404a9eb0a2f3daf7f56e6\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\49b7e53260c404a9eb0a2f3daf7f56e6\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.linking:7.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\710f7d6533e949dec100aa23916a347b\transformed\expo.modules.linking-7.1.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.linking:7.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\710f7d6533e949dec100aa23916a347b\transformed\expo.modules.linking-7.1.7\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa27b8ae0105f42da4d02e6036fc2172\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa27b8ae0105f42da4d02e6036fc2172\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3ab7309334398ea0977c8a6079a31e0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3ab7309334398ea0977c8a6079a31e0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7a56ab820fc9fdf0a911787f27fccf7\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7a56ab820fc9fdf0a911787f27fccf7\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5819168c9856fbf46eb00ae8c5aaa08d\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5819168c9856fbf46eb00ae8c5aaa08d\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f71c7b7206e7973f01bc15e0cb61b11a\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f71c7b7206e7973f01bc15e0cb61b11a\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c312c62d44405731adb5ff9493a546e7\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c312c62d44405731adb5ff9493a546e7\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26c0a5185c80c2eff4ff2c07430bf1bc\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26c0a5185c80c2eff4ff2c07430bf1bc\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc762884eab1ccc882e88c8a2a07adb5\transformed\hermes-android-0.79.5-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc762884eab1ccc882e88c8a2a07adb5\transformed\hermes-android-0.79.5-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9dc2d75673b1ddc5f92823da9ee0d3e4\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9dc2d75673b1ddc5f92823da9ee0d3e4\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\14571843b74eba9256df3cf83913af6e\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\14571843b74eba9256df3cf83913af6e\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\135027b9f17a553df1ae07f8191e0e02\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\135027b9f17a553df1ae07f8191e0e02\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d64d971eb9486b94b61cb4a12a37b722\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d64d971eb9486b94b61cb4a12a37b722\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4fb2a2b87176ed3a593f3493ec69a7c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4fb2a2b87176ed3a593f3493ec69a7c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ec5bda675fb0123fca261876e7e2609\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ec5bda675fb0123fca261876e7e2609\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26c3b200750d3ba1b9e51cf82e0423ee\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26c3b200750d3ba1b9e51cf82e0423ee\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba02d1b07cbf5634c1e8c785edfe4930\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba02d1b07cbf5634c1e8c785edfe4930\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14d1256b203a661e073c238988e62135\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14d1256b203a661e073c238988e62135\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1606e9aded3459c6ed9cfc6e8bbc9f8\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1606e9aded3459c6ed9cfc6e8bbc9f8\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e6899ecdb43c673ff71e1a8dde09cb\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e6899ecdb43c673ff71e1a8dde09cb\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5adea9e76276a7a7aa263dfa62ba5b32\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5adea9e76276a7a7aa263dfa62ba5b32\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5338620589e3d5ecd2308e61ee9b273f\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5338620589e3d5ecd2308e61ee9b273f\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9970e86d3d8ec320aeeff3c5deadaad5\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9970e86d3d8ec320aeeff3c5deadaad5\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79a6adbe0dc8a96797476d070ae79d0e\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79a6adbe0dc8a96797476d070ae79d0e\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e66072023c19c861b98fb96aa058203\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e66072023c19c861b98fb96aa058203\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af7c7096ec59499e3543e60849ae376\transformed\avif-1.1.1.14d8e3c4\AndroidManifest.xml:4:5-44
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af7c7096ec59499e3543e60849ae376\transformed\avif-1.1.1.14d8e3c4\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
	android:resource
		ADDED from [:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
	android:name
		ADDED from [:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
uses-permission#android.permission.WAKE_LOCK
ADDED from [:react-native-firebase_auth] D:\dev\Zayton\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
	android:name
		ADDED from [:react-native-firebase_auth] D:\dev\Zayton\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:react-native-firebase_auth] D:\dev\Zayton\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [BareExpo:expo.modules.image:2.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e48a11f88c3f45b6a25ca3a530edcfb2\transformed\expo.modules.image-2.3.2\AndroidManifest.xml:12:5-79
MERGED from [BareExpo:expo.modules.image:2.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e48a11f88c3f45b6a25ca3a530edcfb2\transformed\expo.modules.image-2.3.2\AndroidManifest.xml:12:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79b7baaf501e70367516e3b80eb83462\transformed\recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79b7baaf501e70367516e3b80eb83462\transformed\recaptcha-18.6.1\AndroidManifest.xml:8:5-79
	android:name
		ADDED from [:react-native-firebase_auth] D:\dev\Zayton\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
meta-data#app_data_collection_default_enabled
ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
	android:value
		ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
	android:name
		ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70cc3ca3259d8dac5931c14081011531\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70cc3ca3259d8dac5931c14081011531\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	tools:targetApi
		ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-32
	android:directBootAware
		ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
	android:name
		ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
meta-data#com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar
ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
provider#io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider
ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
	android:authorities
		ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
	android:exported
		ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
	android:initOrder
		ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
	android:name
		ADDED from [:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
package#host.exp.exponent
ADDED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
	android:name
		ADDED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
activity#expo.modules.devlauncher.launcher.DevLauncherActivity
ADDED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
	android:launchMode
		ADDED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
	android:exported
		ADDED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
	android:theme
		ADDED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
	android:name
		ADDED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-launcher
ADDED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
activity#expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity
ADDED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
	android:screenOrientation
		ADDED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
	android:theme
		ADDED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
	android:name
		ADDED from [:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
activity#expo.modules.devmenu.DevMenuActivity
ADDED from [:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
	android:launchMode
		ADDED from [:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
	android:exported
		ADDED from [:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
	android:theme
		ADDED from [:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
	android:name
		ADDED from [:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-menu
ADDED from [:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79a6adbe0dc8a96797476d070ae79d0e\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79a6adbe0dc8a96797476d070ae79d0e\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79a6adbe0dc8a96797476d070ae79d0e\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c8639f7c924b1c08482d0e5be29b1\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c8639f7c924b1c08482d0e5be29b1\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c8639f7c924b1c08482d0e5be29b1\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ce214f64706bbaa1149d5e3a6c3d23\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ce214f64706bbaa1149d5e3a6c3d23\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ce214f64706bbaa1149d5e3a6c3d23\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ce214f64706bbaa1149d5e3a6c3d23\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70cc3ca3259d8dac5931c14081011531\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70cc3ca3259d8dac5931c14081011531\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70cc3ca3259d8dac5931c14081011531\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79b7baaf501e70367516e3b80eb83462\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79b7baaf501e70367516e3b80eb83462\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d65d6ca76628701ccdf0d352ae5800e\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:8:9-12:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d65d6ca76628701ccdf0d352ae5800e\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:13-90
	android:name
		ADDED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d65d6ca76628701ccdf0d352ae5800e\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:21-87
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\720b5c07abf3dc94b013128bf7fdc5ec\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\720b5c07abf3dc94b013128bf7fdc5ec\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\720b5c07abf3dc94b013128bf7fdc5ec\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4688fae6cbe4dac0f7e1deff2257c956\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4688fae6cbe4dac0f7e1deff2257c956\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3ab7309334398ea0977c8a6079a31e0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3ab7309334398ea0977c8a6079a31e0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.omaraglan96.zayton.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.omaraglan96.zayton.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4688fae6cbe4dac0f7e1deff2257c956\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4688fae6cbe4dac0f7e1deff2257c956\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4688fae6cbe4dac0f7e1deff2257c956\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7740120f109f93963bab9b03c123192f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7740120f109f93963bab9b03c123192f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7740120f109f93963bab9b03c123192f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
