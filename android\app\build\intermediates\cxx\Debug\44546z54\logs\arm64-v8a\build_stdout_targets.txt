ninja: Entering directory `D:\dev\Zayton\android\app\.cxx\Debug\44546z54\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: D:/dev/Zayton/android/app/.cxx/Debug/44546z54/arm64-v8a
[0/2] Re-checking globbed directories...
[1/10] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o
[2/10] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o
[3/10] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o
[4/10] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o
[5/10] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o
[6/10] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[7/10] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o
[8/10] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o
[9/10] Building CXX object CMakeFiles/appmodules.dir/D_/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[10/10] Linking CXX shared library D:\dev\Zayton\android\app\build\intermediates\cxx\Debug\44546z54\obj\arm64-v8a\libappmodules.so
