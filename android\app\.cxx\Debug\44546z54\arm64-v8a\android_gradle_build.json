{"buildFiles": ["D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\dev\\Zayton\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\dev\\<PERSON>ayton\\android\\app\\.cxx\\Debug\\44546z54\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\dev\\<PERSON>ayton\\android\\app\\.cxx\\Debug\\44546z54\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "appmodules", "output": "D:\\dev\\Zayton\\android\\app\\build\\intermediates\\cxx\\Debug\\44546z54\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": ["D:\\dev\\Zayton\\android\\app\\build\\intermediates\\cxx\\Debug\\44546z54\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "D:\\dev\\Zayton\\android\\app\\build\\intermediates\\cxx\\Debug\\44546z54\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "D:\\dev\\Zayton\\android\\app\\build\\intermediates\\cxx\\Debug\\44546z54\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so"]}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNCWebViewSpec"}, "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNEdgeToEdge"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rngesturehandler_codegen"}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnreanimated"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnscreens", "output": "D:\\dev\\Zayton\\android\\app\\build\\intermediates\\cxx\\Debug\\44546z54\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": []}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnsvg", "output": "D:\\dev\\Zayton\\android\\app\\build\\intermediates\\cxx\\Debug\\44546z54\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_safeareacontext", "output": "D:\\dev\\Zayton\\android\\app\\build\\intermediates\\cxx\\Debug\\44546z54\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}