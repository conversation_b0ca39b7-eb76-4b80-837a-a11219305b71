// /app/(drawer)/passwordReset.tsx
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  ImageBackground,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

import auth from '@react-native-firebase/auth';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';

export default function PasswordResetScreen() {
  const router = useRouter();
  const { setConfirmation } = useAuth();
  const [phone, setPhone] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSendOtp = async () => {
    const phoneRegex = /^01[0-9]{9}$/;
    if (!phoneRegex.test(phone)) {
      Alert.alert('خطأ', 'يرجى إدخال رقم هاتف صحيح (01xxxxxxxxx)');
      return;
    }

    setIsLoading(true);
    try {
      // Step 1: Check if the phone number is registered.
      const checkResponse = await fetch("https://api.waleedzaitoun.com/api/auth/check-phone", {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', Accept: 'application/json' },
        body: JSON.stringify({ phone }),
      });

      const checkResult = await checkResponse.json();

      // For password reset, we ONLY proceed if the phone *exists*.
      if (!checkResult.exists) {
        Alert.alert('خطأ', 'هذا الرقم غير مسجل. يرجى إنشاء حساب أولاً.');
        setIsLoading(false);
        return;
      }
      
      // Step 2: Send OTP via Firebase if the phone exists.
      const fullPhoneNumber = `+20${phone.substring(1)}`;
      const confirmationResult = await auth().signInWithPhoneNumber(fullPhoneNumber);
      
      setConfirmation(confirmationResult);

      // Step 3: Navigate to the OTP screen with a flag indicating a reset flow.
      router.push({
        pathname: '/(drawer)/otp',
        params: { 
          phone: fullPhoneNumber, // Pass international format
          localPhone: phone, // Pass local format for the final API call
          flow: 'reset', // This flag is crucial
        },
      });

    } catch (error: any) {
      console.error("Password Reset Error:", error);
      Alert.alert('خطأ', 'فشل إرسال رمز التحقق. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ThemedView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <ImageBackground
            source={require('@/assets/backgrounds/hero-background.jpg')}
            style={styles.heroSection}>
            <LinearGradient
              colors={['transparent', 'rgba(16, 20, 30, 0.9)', '#10141E']}
              locations={[0.5, 1, 1]}
              style={styles.heroGradient}
            />
            <View style={styles.heroContent}>
              <ThemedText style={styles.heroTitle}>إعادة تعيين كلمة السر</ThemedText>
              <ThemedText style={styles.heroSubtitle}>
                أدخل رقم هاتفك المسجل لإرسال رمز التحقق
              </ThemedText>
            </View>
          </ImageBackground>

          <View style={styles.formContainer}>
            <View style={styles.labeledInputContainer}>
              <ThemedText style={styles.label}>رقم الهاتف</ThemedText>
              <View style={styles.inputWrapper}>
                <TextInput
                  placeholder="مثال : 01023456789"
                  placeholderTextColor={Colors.dark.icon}
                  style={styles.input}
                  selectionColor={Colors.dark.tint}
                  keyboardType="phone-pad"
                  value={phone}
                  onChangeText={setPhone}
                />
              </View>
            </View>

            <TouchableOpacity style={[styles.button, isLoading && styles.buttonDisabled]} onPress={handleSendOtp} disabled={isLoading}>
              <ThemedText style={styles.buttonText}>{isLoading ? 'جاري الإرسال...' : 'إرسال الرمز'}</ThemedText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

// Updated Styles
const styles = StyleSheet.create({
  container: { flex: 1 },
  scrollContainer: { flexGrow: 1 },
  heroSection: { height: 700, justifyContent: 'flex-end', alignItems: 'center' },
  heroGradient: { ...StyleSheet.absoluteFillObject },
  heroContent: { paddingHorizontal: 20, paddingBottom: 60, alignItems: 'center' },
  heroTitle: { fontFamily: 'RubikBold', fontSize: 28, color: '#FFFFFF', textAlign: 'center' },
  heroSubtitle: { fontFamily: 'GESSTwoLight', fontSize: 17, color: '#FFFFFF', textAlign: 'center', lineHeight: 28, marginTop: 12 },
  formContainer: { padding: 20, backgroundColor: Colors.dark.background, paddingBottom: 30 },
  labeledInputContainer: { width: '100%', marginBottom: 25 },
  label: { fontFamily: 'GE_SS_Text_Medium', fontSize: 20, color: '#007BFF', textAlign: 'right', marginBottom: 10 },
  inputWrapper: { borderColor: '#007BFF', borderWidth: 1, borderRadius: 1, height: 65, justifyContent: 'center' },
  input: { color: '#FFFFFF', fontFamily: 'GESSTwo', fontSize: 16, textAlign: 'right', paddingHorizontal: 15, width: '100%', height: '100%' },
  button: { backgroundColor: '#007BFF', paddingVertical: 18, borderRadius: 15, alignItems: 'center', marginTop: 20 },
  buttonDisabled: { backgroundColor: '#555' },
  buttonText: { fontFamily: 'GE_SS_Two_Bold', color: 'white', fontSize: 18 },
});