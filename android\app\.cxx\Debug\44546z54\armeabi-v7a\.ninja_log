# ninja log v5
3	93	0	D:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/CMakeFiles/cmake.verify_globs	70eab81b4fed0b85
38800	57257	7752361952180380	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3ab5fe48a452a57eb5718883907c7eba/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	12cf3c2e6e3bfc28
68946	89300	7752362272370405	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	c995c1d2fc91e15c
76133	94327	7752362322330382	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o	5a6052c68ecce68
32481	45496	7752361833815101	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/037303a3626942774a233183d6d53c2b/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o	f3f38483d11fa84b
15028	31651	7752361695885114	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	255806fe1bcf1e8a
124	14166	7752361519495095	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	d1d181bf20e7571a
71386	90037	7752362280190424	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o	5330edc2b5b99b69
67188	85191	7752362231250410	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/26fefe7d2ba27d8025af95104418f116/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	b58048610ff784d1
89301	91121	7752362287260431	D:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnscreens.so	443751c7ee6fc285
42653	59422	7752361973230366	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/917f708e242e4d8be12911227a568149/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o	cfc747b17f03392d
73	19517	7752361574555094	CMakeFiles/appmodules.dir/OnLoad.cpp.o	51560289e55c224c
106	13599	7752361515455096	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	7337a37b6f75b1cd
88	17959	7752361559145100	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	4e942f23affd6f51
63399	84014	7752362219340410	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/26fefe7d2ba27d8025af95104418f116/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	8fb168dd6f2701e5
139	15026	7752361529875098	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	1de5d338e5aeb6a4
224	17183	7752361548895105	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	6ddaadaf2bb3c849
200	16928	7752361548295109	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	3d4d3b81c52119a6
57	63398	7752362010030383	CMakeFiles/appmodules.dir/D_/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	1120e2f2384fe33d
185	17566	7752361554745104	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	47ac1514637c894a
14168	25254	7752361632295102	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	1534a1bad9d17a2b
242	16619	7752361545665093	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	f0f0b36702dac5f4
29554	38519	7752361764485094	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	8910363f319bbf2f
17185	27611	7752361655535077	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	b9afa45281439a98
16622	29553	7752361674405117	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	843d8144b8c82220
61430	74070	7752362120350386	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	1ff4debc9ee09186
17082	30724	7752361686275136	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	dd6bde1b42b7dcbc
13603	32011	7752361699095113	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	4eb1086d0ce12de4
95307	108159	7752362461260436	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/c01f95d0ffa395a187816a33968304b9/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o	5a7d18e55d1e1847
17568	32480	7752361704185101	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	5b8c04d9afe32271
17988	29185	7752361671705102	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	e70a061f11e521c8
57258	75469	7752362134370414	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	38f3a395dca482e
25255	38799	7752361767815074	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	ec1f547f8f3979e
104936	118031	7752362560240402	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	8a0842ae81e3a7eb
19518	33084	7752361710385074	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	ce40fa6fb2d05621
44517	64762	7752362027190402	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	dd7edbc244bb6e50
30868	42652	7752361806125116	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	554a093a7ead00a2
27613	41201	7752361790545104	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	27f3115ded2747e8
41269	53114	7752361911035108	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3ab5fe48a452a57eb5718883907c7eba/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o	19bbfa4fbe00f9fc
74071	87497	7752362254760401	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o	ff0dda279c5b7fbe
31653	44968	7752361829565117	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/917f708e242e4d8be12911227a568149/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	237f556af4fb0f3
33085	52079	7752361899615118	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4060490531b5fb00f72034607561b9fc/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	1d03efe13ea19b4e
32012	44516	7752361824425121	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3953892f01f03e92fc63e80e328f0d4a/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o	6c45bfa73a682d5c
87498	104556	7752362425230418	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o	a8291695755ce852
29186	47249	7752361851655084	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8a26c049427bcc82f1d830398b2cfb87/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	dd2428ab6e82bf65
84016	96871	7752362348410410	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o	5d08765705d5e224
38520	57364	7752361953310404	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3ab5fe48a452a57eb5718883907c7eba/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o	b85451bb9242e5d1
45498	65809	7752362037700400	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/D_/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	744605bf63b0fc25
75470	91102	7752362290110585	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o	56db0f7d9c2f27d5
57365	71385	7752362093080420	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	76ad790aed818bef
80306	93590	7752362315680428	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/c01f95d0ffa395a187816a33968304b9/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o	167e67fb29a54c6e
59423	61429	7752361991660405	D:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_safeareacontext.so	d0ff80b56ebe80cf
47447	68668	7752362065730379	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	696fb87e943f862c
53116	67187	7752362051680402	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/D_/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	e64d8c11265862ca
44969	61667	7752361996420413	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	942b0ca988bc1bbc
61668	80305	7752362182380385	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	e99c220d43fe4ee7
64763	76132	7752362141040396	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	febea21b1d7e8ca2
94621	104934	7752362429010403	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o	aa1ac5a0425b5952
65810	80092	7752362179820425	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9bf7d20490a9d83c10546c1158a394f6/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	8540fd6db644ce2
52080	81482	7752362193090404	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/910b561b42ee9ec61144ee4f3e7c7162/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	1a1b998799d6ab3f
96872	113013	7752362510010423	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/c01f95d0ffa395a187816a33968304b9/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o	8639a2c40fad455a
80093	98763	7752362367350424	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o	3397c2f03f3106ca
93592	116961	7752362548610416	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o	e9a68c6505abdbe2
81483	102890	7752362408140393	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/6241aae3cc620ac17b0d3702c983eced/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o	4120d7b543fec7fb
85192	95306	7752362332420433	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o	8257c933d4722a6
109158	118452	7752362564580357	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	384eff0d5ee46476
116962	117422	7752362553690407	D:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnsvg.so	fc7253590187c227
91122	109156	7752362470720394	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o	ed16c1659314aa9e
98764	113844	7752362517450408	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o	8a33f97845efef09
108160	117376	7752362553630378	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	c7deceb58407c64b
91104	105888	7752362438490403	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o	74099e768df12b5e
104557	117611	7752362556030387	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	c46d644b17a29b8a
111235	119798	7752362578051982	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	f73812edb16f7c02
90038	111234	7752362491820422	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o	10c9e776d54440a9
102892	116595	7752362545800421	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	cda65898c16c8e4e
105889	118260	7752362562600396	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	4d18c3f61c48b2d1
119799	120219	7752362581662096	D:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libappmodules.so	a1949790538b6d53
0	29	0	clean	edc044b4fa31ef3a
93	1336	7753115078013924	build.ninja	52a73b9539146726
1	51	0	D:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/CMakeFiles/cmake.verify_globs	70eab81b4fed0b85
121	12057	7753115202123706	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o	6931264c19fe716c
109	13328	7753115215533711	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o	4aa3674dd225344e
97	14834	7753115230663701	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o	c94031c3a7cda9d9
74	15403	7753115236463709	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o	2ce4af3faf09f8ff
134	15590	7753115238473684	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o	5bb811a9fe6607a0
84	16318	7753115245713671	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o	8aa69e3e042e6667
41	16801	7753115250574073	CMakeFiles/appmodules.dir/OnLoad.cpp.o	f04d1d682bd70a49
63	17137	7753115253643652	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o	3e6e0755bb31a0b5
52	31894	7753115399767554	CMakeFiles/appmodules.dir/D_/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	5538c421564349ff
31894	32751	7753115409657804	D:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libappmodules.so	af4a829f95ac2d29
