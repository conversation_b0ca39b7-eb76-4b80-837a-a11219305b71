// /app/(drawer)/_layout.tsx
import { Drawer } from 'expo-router/drawer';

import { CustomDrawerContent } from '@/components/CustomDrawerContent';
import { CustomHeader } from '@/components/CustomHeader';

export default function DrawerLayout() {
  return (
    <Drawer
      // Use our custom component for the drawer's content
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        header: () => <CustomHeader />, // Custom header component
        headerShown: true,
      }}>
      {/* 
        This screen is necessary for the navigator to work, 
        but we hide it from the drawer UI itself since our custom content handles it.
      */}
      <Drawer.Screen
        name="(tabs)"
        options={{
          drawerLabel: () => null, // Hide label
          title: undefined,             // Hide title
          drawerItemStyle: { height: 0 }, // Hide item completely
        }}
      />
      {/* These screens are part of the drawer's stack but hidden from the UI */}
      <Drawer.Screen
        name="login"
        options={{
          drawerLabel: () => null,
          title: undefined,
          drawerItemStyle: { height: 0 },
        }}
      />
      <Drawer.Screen
        name="registrationPassword"
        options={{
          drawerLabel: () => null,
          title: undefined,
          drawerItemStyle: { height: 0 },
        }}
      />
      <Drawer.Screen
        name="otp"
        options={{
          drawerLabel: () => null,
          title: undefined,
          drawerItemStyle: { height: 0 },
        }}
      />
      <Drawer.Screen
        name="passwordReset"
        options={{
          drawerLabel: () => null,
          title: undefined,
          drawerItemStyle: { height: 0 },
        }}
      />
      <Drawer.Screen
        name="createNewPassword"
        options={{
          drawerLabel: () => null,
          title: undefined,
          drawerItemStyle: { height: 0 },
        }}
      />
    </Drawer>
  );
}