// /app/(drawer)/(tabs)/news/index.tsx
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { LinearGradient } from 'expo-linear-gradient';
import { Link } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  ImageBackground,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';

const NewsCard = ({ item }: { item: any }) => (
  <Link href={`/(drawer)/(tabs)/news/${item.id}` as any} asChild>
    <TouchableOpacity style={styles.card}>
      {/* FIX 2: Construct the full image URL */}
      <Image source={{ uri: `https://api.waleedzaitoun.com${item.image_path}` }} style={styles.cardImage} />
      <View style={styles.cardContent}>
        <ThemedText style={styles.cardTitle} numberOfLines={2}>{item.title}</ThemedText>
        <ThemedText style={styles.cardDescription} numberOfLines={3}>{item.description}</ThemedText>
      </View>
    </TouchableOpacity>
  </Link>
);

export default function NewsScreen() {
  const [news, setNews] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchNews = async () => {
      try {
        const response = await fetch('https://api.waleedzaitoun.com/api/news');
        const data = await response.json();
        // FIX 1: The API returns a direct array, so we use `data` directly
        setNews(data); 
      } catch (error) {
        console.error("Failed to fetch news:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchNews();
  }, []);

  const renderHeader = () => (
    <ImageBackground
      source={require('@/assets/backgrounds/hero-background.jpg')}
      style={styles.headerBackground}>
      <LinearGradient
        colors={['rgba(0, 0, 0, 0)', 'rgba(16, 20, 30, 0.62)']}
        style={styles.headerGradient}
      />
      <ThemedText style={styles.mainTitle}>أخر الأخبار</ThemedText>
    </ImageBackground>
  );
  
  if (isLoading) {
    return <ActivityIndicator size="large" color={Colors.dark.tint} style={{flex: 1}} />;
  }

  return (
    <ThemedView style={styles.container}>
      <FlatList
        data={news}
        renderItem={({ item }) => <NewsCard item={item} />}
        keyExtractor={(item) => item.id.toString()}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContainer}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 0, // Correct, as the screen should start from the top edge
  },
  listContainer: {
    paddingBottom: 40,
  },
  // FIX: Reduced height and changed alignment to position the title correctly
  headerBackground: {
    height: 600, // Reduced height to remove the large space
    justifyContent: 'flex-end', // Aligns title to the bottom
    alignItems: 'flex-end', // Aligns title to the right
    padding: 20, // Adds padding so the title isn't stuck to the edges
  },
  headerGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  // FIX: Removed large margin and adjusted font size for better fit
  mainTitle: {
    fontFamily: 'GE_SS_Two_Bold',
    fontSize: 35, // Slightly larger for impact
    color: 'white',
    textAlign: 'right',
    lineHeight: 45,
    
  },
  card: {
    backgroundColor: '#D9D9D9',
    borderRadius: 20,
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 15,
  },
  cardImage: {
    width: '100%',
    height: 300,
    borderRadius: 15,
  },
  cardContent: {
    paddingTop: 15,
  },
  cardTitle: {
    fontFamily: 'GE_SS_Two_Bold',
    fontSize: 17,
    color: '#000000ff',
    textAlign: 'right',
  },
  // Description text color updated
  cardDescription: {
    fontFamily: 'GESSTwoLight',
    fontSize: 16,
    color: '#10141E',
    textAlign: 'right',
    marginTop: 5,
  },
  button: {
    backgroundColor: '#007BFF',
    paddingVertical: 18,
    marginHorizontal: 20,
    borderRadius: 15,
    alignItems: 'center',
    marginTop: 10,
  },
   buttonText: {
    fontFamily: 'GE_SS_Text_Medium',
    color: 'white',
    fontSize: 24,
  },
});