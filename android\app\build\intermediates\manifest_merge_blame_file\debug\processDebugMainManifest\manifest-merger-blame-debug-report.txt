1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.omaraglan96.zayton"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:4:3-75
11-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:2:3-64
12-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission
13-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:3:3-77
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:3:20-75
15        android:maxSdkVersion="32" />
15-->[BareExpo:expo.modules.image:2.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e48a11f88c3f45b6a25ca3a530edcfb2\transformed\expo.modules.image-2.3.2\AndroidManifest.xml:17:9-35
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:5:3-63
16-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:5:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:6:3-78
17-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:6:20-76
18
19    <queries>
19-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:7:3-13:13
20        <intent>
20-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:8:5-12:14
21            <action android:name="android.intent.action.VIEW" />
21-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:9:7-58
21-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:9:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:10:7-67
23-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:10:17-65
24
25            <data android:scheme="https" />
25-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:7-37
25-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:13-35
26        </intent>
27
28        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
28-->[:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
28-->[:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
29        <intent>
29-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
30            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
31        </intent>
32        <intent>
32-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d65d6ca76628701ccdf0d352ae5800e\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:8:9-12:18
33
34            <!-- Required for opening tabs if targeting API 30 -->
35            <action android:name="android.support.customtabs.action.CustomTabsService" />
35-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d65d6ca76628701ccdf0d352ae5800e\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:13-90
35-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d65d6ca76628701ccdf0d352ae5800e\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:21-87
36        </intent>
37    </queries>
38
39    <uses-permission android:name="android.permission.WAKE_LOCK" />
39-->[:react-native-firebase_auth] D:\dev\Zayton\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
39-->[:react-native-firebase_auth] D:\dev\Zayton\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
40    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
40-->[:react-native-firebase_auth] D:\dev\Zayton\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
40-->[:react-native-firebase_auth] D:\dev\Zayton\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
41    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
41-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79b7baaf501e70367516e3b80eb83462\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
41-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79b7baaf501e70367516e3b80eb83462\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
42
43    <permission
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
44        android:name="com.omaraglan96.zayton.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.omaraglan96.zayton.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
48
49    <application
49-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:3-31:17
50        android:name="com.omaraglan96.zayton.MainApplication"
50-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:16-47
51        android:allowBackup="true"
51-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:162-188
52        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8af07a92579850ca0fa9ee28fde6d76\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
53        android:debuggable="true"
54        android:extractNativeLibs="false"
55        android:icon="@mipmap/ic_launcher"
55-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:81-115
56        android:label="@string/app_name"
56-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:48-80
57        android:roundIcon="@mipmap/ic_launcher_round"
57-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:116-161
58        android:supportsRtl="true"
58-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:221-247
59        android:theme="@style/AppTheme"
59-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:14:189-220
60        android:usesCleartextTraffic="true" >
60-->D:\dev\Zayton\android\app\src\debug\AndroidManifest.xml:6:18-53
61        <meta-data
61-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:15:5-83
62            android:name="expo.modules.updates.ENABLED"
62-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:15:16-59
63            android:value="false" />
63-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:15:60-81
64        <meta-data
64-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:16:5-105
65            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
65-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:16:16-80
66            android:value="ALWAYS" />
66-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:16:81-103
67        <meta-data
67-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:17:5-99
68            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
68-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:17:16-79
69            android:value="0" />
69-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:17:80-97
70
71        <activity
71-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:5-30:16
72            android:name="com.omaraglan96.zayton.MainActivity"
72-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:15-43
73            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
73-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:44-134
74            android:exported="true"
74-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:256-279
75            android:launchMode="singleTask"
75-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:135-166
76            android:screenOrientation="portrait"
76-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:280-316
77            android:theme="@style/Theme.App.SplashScreen"
77-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:210-255
78            android:windowSoftInputMode="adjustResize" >
78-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:18:167-209
79            <intent-filter>
79-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:19:7-22:23
80                <action android:name="android.intent.action.MAIN" />
80-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:20:9-60
80-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:20:17-58
81
82                <category android:name="android.intent.category.LAUNCHER" />
82-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:21:9-68
82-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:21:19-66
83            </intent-filter>
84            <intent-filter>
84-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:23:7-29:23
85                <action android:name="android.intent.action.VIEW" />
85-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:9:7-58
85-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:9:15-56
86
87                <category android:name="android.intent.category.DEFAULT" />
87-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:25:9-67
87-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:25:19-65
88                <category android:name="android.intent.category.BROWSABLE" />
88-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:10:7-67
88-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:10:17-65
89
90                <data android:scheme="zayton" />
90-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:7-37
90-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:13-35
91                <data android:scheme="exp+zayton" />
91-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:7-37
91-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:13-35
92            </intent-filter>
93        </activity>
94
95        <provider
95-->[:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
96            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
96-->[:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
97            android:authorities="com.omaraglan96.zayton.fileprovider"
97-->[:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
98            android:exported="false"
98-->[:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
99            android:grantUriPermissions="true" >
99-->[:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
100            <meta-data
100-->[:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
101                android:name="android.support.FILE_PROVIDER_PATHS"
101-->[:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
102                android:resource="@xml/file_provider_paths" />
102-->[:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
103        </provider>
104
105        <meta-data
105-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
106            android:name="app_data_collection_default_enabled"
106-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
107            android:value="true" />
107-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
108
109        <service
109-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
110            android:name="com.google.firebase.components.ComponentDiscoveryService"
110-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
111            android:directBootAware="true"
111-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
112            android:exported="false" >
112-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
113            <meta-data
113-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
114                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
114-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
116            <meta-data
116-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
117                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
117-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
119            <meta-data
119-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70cc3ca3259d8dac5931c14081011531\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
120                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
120-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70cc3ca3259d8dac5931c14081011531\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70cc3ca3259d8dac5931c14081011531\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
122            <meta-data
122-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
123                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
123-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
125        </service>
126
127        <provider
127-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
128            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
128-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
129            android:authorities="com.omaraglan96.zayton.reactnativefirebaseappinitprovider"
129-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
130            android:exported="false"
130-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
131            android:initOrder="99" />
131-->[:react-native-firebase_app] D:\dev\Zayton\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
132
133        <activity
133-->[:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
134            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
134-->[:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
135            android:exported="true"
135-->[:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
136            android:launchMode="singleTask"
136-->[:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
137            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
137-->[:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
138            <intent-filter>
138-->[:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
139                <action android:name="android.intent.action.VIEW" />
139-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:9:7-58
139-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:9:15-56
140
141                <category android:name="android.intent.category.DEFAULT" />
141-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:25:9-67
141-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:25:19-65
142                <category android:name="android.intent.category.BROWSABLE" />
142-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:10:7-67
142-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:10:17-65
143
144                <data android:scheme="expo-dev-launcher" />
144-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:7-37
144-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:13-35
145            </intent-filter>
146        </activity>
147        <activity
147-->[:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
148            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
148-->[:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
149            android:screenOrientation="portrait"
149-->[:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
150            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
150-->[:expo-dev-launcher] D:\dev\Zayton\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
151        <activity
151-->[:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
152            android:name="expo.modules.devmenu.DevMenuActivity"
152-->[:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
153            android:exported="true"
153-->[:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
154            android:launchMode="singleTask"
154-->[:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
155            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
155-->[:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
156            <intent-filter>
156-->[:expo-dev-menu] D:\dev\Zayton\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
157                <action android:name="android.intent.action.VIEW" />
157-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:9:7-58
157-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:9:15-56
158
159                <category android:name="android.intent.category.DEFAULT" />
159-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:25:9-67
159-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:25:19-65
160                <category android:name="android.intent.category.BROWSABLE" />
160-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:10:7-67
160-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:10:17-65
161
162                <data android:scheme="expo-dev-menu" />
162-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:7-37
162-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:13-35
163            </intent-filter>
164        </activity>
165
166        <meta-data
166-->[:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
167            android:name="org.unimodules.core.AppLoader#react-native-headless"
167-->[:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
168            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
168-->[:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
169        <meta-data
169-->[:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
170            android:name="com.facebook.soloader.enabled"
170-->[:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
171            android:value="true" />
171-->[:expo-modules-core] D:\dev\Zayton\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
172
173        <activity
173-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c8639f7c924b1c08482d0e5be29b1\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
174            android:name="com.facebook.react.devsupport.DevSettingsActivity"
174-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c8639f7c924b1c08482d0e5be29b1\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
175            android:exported="false" />
175-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24c8639f7c924b1c08482d0e5be29b1\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
176        <activity
176-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
177            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
177-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
178            android:excludeFromRecents="true"
178-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
179            android:exported="true"
179-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
180            android:launchMode="singleTask"
180-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
181            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
181-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
182            <intent-filter>
182-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
183                <action android:name="android.intent.action.VIEW" />
183-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:9:7-58
183-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:9:15-56
184
185                <category android:name="android.intent.category.DEFAULT" />
185-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:25:9-67
185-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:25:19-65
186                <category android:name="android.intent.category.BROWSABLE" />
186-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:10:7-67
186-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:10:17-65
187
188                <data
188-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:7-37
189                    android:host="firebase.auth"
190                    android:path="/"
191                    android:scheme="genericidp" />
191-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:13-35
192            </intent-filter>
193        </activity>
194        <activity
194-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
195            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
195-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
196            android:excludeFromRecents="true"
196-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
197            android:exported="true"
197-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
198            android:launchMode="singleTask"
198-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
199            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
199-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
200            <intent-filter>
200-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18759098e5e92160df4f49d49f235980\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
201                <action android:name="android.intent.action.VIEW" />
201-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:9:7-58
201-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:9:15-56
202
203                <category android:name="android.intent.category.DEFAULT" />
203-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:25:9-67
203-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:25:19-65
204                <category android:name="android.intent.category.BROWSABLE" />
204-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:10:7-67
204-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:10:17-65
205
206                <data
206-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:7-37
207                    android:host="firebase.auth"
208                    android:path="/"
209                    android:scheme="recaptcha" />
209-->D:\dev\Zayton\android\app\src\main\AndroidManifest.xml:11:13-35
210            </intent-filter>
211        </activity>
212
213        <service
213-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
214            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
214-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
215            android:enabled="true"
215-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
216            android:exported="false" >
216-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
217            <meta-data
217-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
218                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
218-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
219                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
219-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
220        </service>
221
222        <activity
222-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
223            android:name="androidx.credentials.playservices.HiddenActivity"
223-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
224            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
224-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
225            android:enabled="true"
225-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
226            android:exported="false"
226-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
227            android:fitsSystemWindows="true"
227-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
228            android:theme="@style/Theme.Hidden" >
228-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d2ee2a81afc981a9c242d452fdfb9fa\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
229        </activity>
230        <activity
230-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
231            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
231-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
232            android:excludeFromRecents="true"
232-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
233            android:exported="false"
233-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
234            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
234-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
235        <!--
236            Service handling Google Sign-In user revocation. For apps that do not integrate with
237            Google Sign-In, this service will never be started.
238        -->
239        <service
239-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
240            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
240-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
241            android:exported="true"
241-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
242            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
242-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
243            android:visibleToInstantApps="true" />
243-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eea4239caa2d70a7242275f63c2e8e7\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
244
245        <activity
245-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ce214f64706bbaa1149d5e3a6c3d23\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
246            android:name="com.google.android.gms.common.api.GoogleApiActivity"
246-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ce214f64706bbaa1149d5e3a6c3d23\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
247            android:exported="false"
247-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ce214f64706bbaa1149d5e3a6c3d23\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
248            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
248-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ce214f64706bbaa1149d5e3a6c3d23\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
249
250        <provider
250-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
251            android:name="com.google.firebase.provider.FirebaseInitProvider"
251-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
252            android:authorities="com.omaraglan96.zayton.firebaseinitprovider"
252-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
253            android:directBootAware="true"
253-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
254            android:exported="false"
254-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
255            android:initOrder="100" />
255-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf1b98613e2b973795d4798a4b34eec6\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
256        <provider
256-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
257            android:name="expo.modules.filesystem.FileSystemFileProvider"
257-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
258            android:authorities="com.omaraglan96.zayton.FileSystemFileProvider"
258-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
259            android:exported="false"
259-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
260            android:grantUriPermissions="true" >
260-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\28418a7f961b9fcdf50036956ce7d856\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
261            <meta-data
261-->[:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
262                android:name="android.support.FILE_PROVIDER_PATHS"
262-->[:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
263                android:resource="@xml/file_system_provider_paths" />
263-->[:react-native-webview] D:\dev\Zayton\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
264        </provider>
265
266        <meta-data
266-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\720b5c07abf3dc94b013128bf7fdc5ec\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
267            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
267-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\720b5c07abf3dc94b013128bf7fdc5ec\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
268            android:value="GlideModule" />
268-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\720b5c07abf3dc94b013128bf7fdc5ec\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
269
270        <provider
270-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
271            android:name="androidx.startup.InitializationProvider"
271-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
272            android:authorities="com.omaraglan96.zayton.androidx-startup"
272-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
273            android:exported="false" >
273-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
274            <meta-data
274-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
275                android:name="androidx.emoji2.text.EmojiCompatInitializer"
275-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
276                android:value="androidx.startup" />
276-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f33dbbca0012100a51b50c5f75146ea8\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
277            <meta-data
277-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4688fae6cbe4dac0f7e1deff2257c956\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
278                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
278-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4688fae6cbe4dac0f7e1deff2257c956\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
279                android:value="androidx.startup" />
279-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4688fae6cbe4dac0f7e1deff2257c956\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
280            <meta-data
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
281                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
281-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
282                android:value="androidx.startup" />
282-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
283        </provider>
284
285        <meta-data
285-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7740120f109f93963bab9b03c123192f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
286            android:name="com.google.android.gms.version"
286-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7740120f109f93963bab9b03c123192f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
287            android:value="@integer/google_play_services_version" />
287-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7740120f109f93963bab9b03c123192f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
288
289        <receiver
289-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
290            android:name="androidx.profileinstaller.ProfileInstallReceiver"
290-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
291            android:directBootAware="false"
291-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
292            android:enabled="true"
292-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
293            android:exported="true"
293-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
294            android:permission="android.permission.DUMP" >
294-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
295            <intent-filter>
295-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
296                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
296-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
296-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
297            </intent-filter>
298            <intent-filter>
298-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
299                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
299-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
299-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
300            </intent-filter>
301            <intent-filter>
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
302                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
302-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
302-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
303            </intent-filter>
304            <intent-filter>
304-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
305                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
305-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
305-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ad84e2fa80f9223fe366cdb5ce916bb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
306            </intent-filter>
307        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
308        <activity
308-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
309            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
309-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
310            android:exported="false"
310-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
311            android:stateNotNeeded="true"
311-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
312            android:theme="@style/Theme.PlayCore.Transparent" />
312-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bab50c4012ee3622be83fcce48a5157c\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
313    </application>
314
315</manifest>
