// /components/CountdownSection.tsx
import React, { memo, useEffect, useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Link } from 'expo-router';

import { ThemedText } from './ThemedText';

// The target date for the countdown.
const TARGET_DATE = new Date('2025-08-04T00:00:00');

// Helper function to add a leading zero if the number is less than 10
const formatTime = (time: number) => String(time).padStart(2, '0');

// Using React.memo to prevent unnecessary re-renders from the parent FlatList
export const CountdownSection = memo(() => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0, // 1. Add seconds to the state
  });

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date();
      const difference = TARGET_DATE.getTime() - now.getTime();

      if (difference <= 0) {
        // Countdown is over
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
        clearInterval(timer);
        return;
      }

      // 2. Calculate time left, including seconds
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000); // Calculate seconds

      setTimeLeft({ days, hours, minutes, seconds }); // Update state with seconds
    }, 1000); // Update every second

    // Cleanup function to clear the interval when the component unmounts
    return () => clearInterval(timer);
  }, []); // The empty dependency array ensures this effect runs only once

  return (
    <View style={styles.container}>
      {/* Main Title */}
      <ThemedText style={styles.title}>موعدنا يوم</ThemedText>
      <ThemedText style={styles.dateText}>٤ و ٥ أغسطس ٢٠٢٥</ThemedText>

      {/* Countdown Card */}
      <View style={styles.card}>
        <ThemedText style={styles.cardTitle}>باقى من الزمن</ThemedText>
        <View style={styles.timerContainer}>
          {/* Days */}
          <View style={styles.timeBlock}>
            <ThemedText style={styles.timeNumber}>{formatTime(timeLeft.days)}</ThemedText>
            <ThemedText style={styles.timeLabel}>يوم</ThemedText>
          </View>
          <ThemedText style={styles.timeSeparator}>:</ThemedText>
          {/* Hours */}
          <View style={styles.timeBlock}>
            <ThemedText style={styles.timeNumber}>{formatTime(timeLeft.hours)}</ThemedText>
            <ThemedText style={styles.timeLabel}>ساعة</ThemedText>
          </View>
          <ThemedText style={styles.timeSeparator}>:</ThemedText>
          {/* Minutes */}
          <View style={styles.timeBlock}>
            <ThemedText style={styles.timeNumber}>{formatTime(timeLeft.minutes)}</ThemedText>
            <ThemedText style={styles.timeLabel}>دقيقة</ThemedText>
          </View>
          {/* 3. Add the Seconds block */}
          <ThemedText style={styles.timeSeparator}>:</ThemedText>
          <View style={styles.timeBlock}>
            <ThemedText style={styles.timeNumber}>{formatTime(timeLeft.seconds)}</ThemedText>
            <ThemedText style={styles.timeLabel}>ثانية</ThemedText>
          </View>
        </View>
      </View>

      {/* Button */}
      {/* 4. Add the button to navigate to the registration page */}
      <Link href="/(drawer)/(tabs)/register" asChild>
      <TouchableOpacity style={styles.button}>
        <ThemedText style={styles.buttonText}>انضم لحملتنا الانتخابية</ThemedText>
      </TouchableOpacity>
      </Link>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    paddingVertical: 10,
    alignItems: 'center',
    backgroundColor: '#020304ff',
  },
  title: {
    fontFamily: 'GE_SS_Two_Bold',
    fontSize: 28,
    color: '#007BFF',
    textAlign: 'center',
    lineHeight: 30,
  },
  dateText: {
    fontFamily: 'RubikBold',
    fontSize: 30,
    color: 'white',
    textAlign: 'center',
    lineHeight: 40,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    width: '95%',
    alignItems: 'center',
    marginTop: 30,
  },
  cardTitle: {
    fontFamily: 'GE_SS_Two_Bold',
    fontSize: 22,
    color: '#007BFF',
    textAlign: 'center',
    lineHeight: 30,
  },
  timerContainer: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    marginTop: 10,
  },
  timeBlock: {
    alignItems: 'center',
    minWidth: 40,
  },
  timeNumber: {
    fontFamily: 'RubikBold',
    fontSize: 32,
    color: '#007BFF',
    lineHeight: 40,
  },
  timeLabel: {
    fontFamily: 'GE_SS_Two_Bold',
    fontSize: 16,
    color: '#007BFF',
    marginTop: -5,
  },
  timeSeparator: {
    fontFamily: 'RubikBold',
    fontSize: 48,
    color: '#007BFF',
    marginHorizontal: 10,
    paddingBottom: 20,
  },
  button: {
    backgroundColor: '#007BFF',
    paddingVertical: 18,
    width: '90%',
    borderRadius: 15,
    alignItems: 'center',
    marginTop: 30,
    marginBottom: 20, // Added margin to the bottom for better spacing
  },
  buttonText: {
    fontFamily: 'GE_SS_Two_Bold',
    color: 'white',
    fontSize: 20,
  },
});

CountdownSection.displayName = 'CountdownSection';