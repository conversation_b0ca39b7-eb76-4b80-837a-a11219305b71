@echo off
"C:\\Program Files\\Eclipse Adoptium\\jdk-*********-hotspot\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  arm64-v8a ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  27 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging15503974085850070945\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b24c8639f7c924b1c08482d0e5be29b1\\transformed\\react-android-0.79.5-debug\\prefab" ^
  "D:\\dev\\Zayton\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\1y4i1p41" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cc762884eab1ccc882e88c8a2a07adb5\\transformed\\hermes-android-0.79.5-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9970e86d3d8ec320aeeff3c5deadaad5\\transformed\\fbjni-0.7.0\\prefab"
