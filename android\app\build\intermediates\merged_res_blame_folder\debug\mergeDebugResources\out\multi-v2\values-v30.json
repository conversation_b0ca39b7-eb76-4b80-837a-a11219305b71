{"logs": [{"outputFile": "com.omaraglan96.zayton.app-mergeDebugResources-65:/values-v30/values-v30.xml", "map": [{"source": "D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values-v30\\values-v30.xml", "from": {"startLines": "2,8,14,20,26,32,38,44", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,472,869,1306,1723,2160,2613,3046", "endLines": "7,13,19,25,31,37,43,49", "endColumns": "12,12,12,12,12,12,12,12", "endOffsets": "467,864,1301,1718,2155,2608,3041,3458"}, "to": {"startLines": "8,14,20,26,32,38,44,50", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "438,855,1252,1689,2106,2543,2996,3429", "endLines": "13,19,25,31,37,43,49,55", "endColumns": "12,12,12,12,12,12,12,12", "endOffsets": "850,1247,1684,2101,2538,2991,3424,3841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c06210739c1eceecdcd152c368ed657c\\transformed\\core-splashscreen-1.2.0-alpha02\\res\\values-v30\\values-v30.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "7", "endColumns": "12", "endOffsets": "433"}}]}]}