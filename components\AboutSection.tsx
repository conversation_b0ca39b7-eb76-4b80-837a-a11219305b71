import { Image } from 'expo-image';
import React from 'react';
import { StyleSheet, View } from 'react-native';

import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

export function AboutSection() {
  return (
    <ThemedView style={styles.container}>
      {/* Text Content */}
      <View style={styles.textContainer}>
        <ThemedText style={styles.titleText}>من هو؟</ThemedText>
        <ThemedText style={styles.nameText}>وليد محمد زيتون</ThemedText>
      </View>

      {/* Person Image */}
      <Image
        // IMPORTANT: Replace with the actual path to your image
        source={require('@/assets/backgrounds/about-background.png')}
        style={styles.personImage}
        contentFit="contain"
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 700, // Adjust height as needed
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 70,
    position: 'relative', // Needed for absolute positioning of children
  },
  textContainer: {
    alignItems: 'center',
    zIndex: 1, // Ensure text is above the image
  },
  titleText: {
    fontFamily: 'GE_SS_Two_Bold',
    fontSize: 40,
    textAlign: 'center',
    color: '#0a7ea4', // Using the app's tint color
    lineHeight: 45,
  },
  nameText: {
    fontFamily: 'RubikBold',
    fontSize: 35,
    color: 'white',
    textAlign: 'center',
    width: '80%',
    lineHeight: 40,
  },
  personImage: {
    width: '185%',
    height: '185%', // Adjust to fit the design
    bottom: 60,
  },
});