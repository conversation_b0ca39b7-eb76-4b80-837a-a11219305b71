// /app/(drawer)/otp.tsx
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useRef, useState } from 'react';
import {
  Alert,
  ImageBackground,
  KeyboardAvoidingView,
  NativeSyntheticEvent,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TextInputKeyPressEventData,
  TouchableOpacity,
  View,
} from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';

export default function OTPScreen() {
  const router = useRouter();
  // Add 'flow' and 'localPhone' to the expected params
  const params = useLocalSearchParams<{ name?: string; phone: string; flow?: 'reset'; localPhone?: string }>();
  
  const { confirmation } = useAuth();
  
  const [isLoading, setIsLoading] = useState(false);
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const inputs = useRef<Array<TextInput | null>>([]);

  const handleOtpChange = (text: string, index: number) => {
    if (/^[0-9]$/.test(text) || text === '') {
      const newOtp = [...otp];
      newOtp[index] = text;
      setOtp(newOtp);
      if (text && index < 5) {
        inputs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyPress = (
    e: NativeSyntheticEvent<TextInputKeyPressEventData>,
    index: number
  ) => {
    if (e.nativeEvent.key === 'Backspace' && otp[index] === '' && index > 0) {
      inputs.current[index - 1]?.focus();
    }
  };
  
  const handleConfirm = async () => {
    const code = otp.join('');
    const { flow, localPhone, ...registrationParams } = params;
    
    if (code.length !== 6) {
        Alert.alert('خطأ', 'يرجى إدخال الرمز المكون من 6 أرقام.');
        return;
    }

    if (!confirmation) {
        Alert.alert('خطأ', 'فشل التحقق. يرجى العودة والمحاولة مرة أخرى.');
        router.back();
        return;
    }

    setIsLoading(true);
    try {
      await confirmation.confirm(code);
      
      if (flow === 'reset') {
        router.push({
          pathname: '/(drawer)/createNewPassword',
          params: { phone: localPhone },
        });
      } else {
        router.push({
          pathname: '/(drawer)/registrationPassword',
          params: registrationParams,
        });
      }

    } catch (error: any) {
      console.error('Confirm Code Error:', error);
      if (error.code === 'auth/invalid-verification-code' || error.code === 'auth/session-expired') {
        Alert.alert('خطأ', 'الرمز الذي أدخلته غير صحيح أو انتهت صلاحيته. يرجى المحاولة مرة أخرى.');
      } else {
        Alert.alert('خطأ', 'فشل التحقق من الرمز. يرجى المحاولة مرة أخرى.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ThemedView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <ImageBackground
            source={require('@/assets/backgrounds/hero-background.jpg')}
            style={styles.heroSection}>
            <LinearGradient
              colors={['transparent', 'rgba(16, 20, 30, 0.9)', '#10141E']}
              locations={[0.5, 1, 1]}
              style={styles.heroGradient}
            />
            <View style={styles.heroContent}>
              <ThemedText style={styles.heroTitle}>
                {params.name ? `أهلاً بك ${params.name}` : 'تأكيد الرمز'}
              </ThemedText>
              
              {/* --- THIS IS THE UPDATED PART --- */}
              <ThemedText style={styles.heroSubtitle}>
                {params.flow === 'reset'
                  ? 'لتأكيد هويتك، برجاء إدخال الرمز المرسل إلى هاتفك'
                  : 'لاكمال تسجيل حسابك برجاء تأكيد رقم هاتفك'
                }
              </ThemedText>

            </View>
          </ImageBackground>

          <View style={styles.formContainer}>
            <View style={styles.labeledInputContainer}>
              <ThemedText style={styles.label}>تأكيد رقم الهاتف</ThemedText>
              <View style={styles.inputWrapper}>
                 <ThemedText style={styles.input}>{params.phone}</ThemedText>
              </View>
            </View>

            <ThemedText style={styles.infoText}>
              لقد قمنا بارسال رسالة نصية تحتوى على رمز تحقق مكون من 6 أرقام.
            </ThemedText>

            <View style={styles.otpContainer}>
              {otp.map((digit, index) => (
                <TextInput
                  key={index}
                  ref={(ref) => { inputs.current[index] = ref; }}
                  style={styles.otpBox}
                  keyboardType="number-pad"
                  maxLength={1}
                  onChangeText={(text) => handleOtpChange(text, index)}
                  onKeyPress={(e) => handleKeyPress(e, index)}
                  value={digit}
                  selectionColor={Colors.dark.tint}
                  editable={!isLoading}
                />
              ))}
            </View>

            <TouchableOpacity style={[styles.button, isLoading && styles.buttonDisabled]} onPress={handleConfirm} disabled={isLoading}>
              <ThemedText style={styles.buttonText}>{isLoading ? 'جاري التحقق...' : 'تأكيد'}</ThemedText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

// All styles remain the same
const styles = StyleSheet.create({
  container: { flex: 1 },
  scrollContainer: { flexGrow: 1 },
  heroSection: { height: 700, justifyContent: 'flex-end', alignItems: 'center' },
  heroGradient: { ...StyleSheet.absoluteFillObject },
  heroContent: { paddingHorizontal: 20, paddingBottom: 40, alignItems: 'center' },
  heroTitle: { fontFamily: 'RubikBold', fontSize: 25, color: '#FFFFFF', lineHeight: 30, textAlign: 'center' },
  heroSubtitle: { fontFamily: 'GESSTwoLight', fontSize: 17, color: '#FFFFFF', textAlign: 'center', lineHeight: 28, marginTop: 12 },
  formContainer: { padding: 20, backgroundColor: Colors.dark.background, paddingBottom: 30 },
  labeledInputContainer: { width: '100%', marginBottom: 25 },
  label: { fontFamily: 'GE_SS_Text_Medium', fontSize: 20, color: '#007BFF', textAlign: 'right', marginBottom: 10 },
  inputWrapper: { flexDirection: 'row-reverse', alignItems: 'center', backgroundColor: 'transparent', borderColor: '#444', borderWidth: 1, borderRadius: 1, height: 65, paddingHorizontal: 15 },
  input: { flex: 1, color: '#FFFFFF', fontFamily: 'GESSTwo', fontSize: 16, textAlign: 'right', paddingHorizontal: 15 },
  infoText: { fontFamily: 'GESSTwoLight', fontSize: 15, color: '#FFFFFF', textAlign: 'right', marginBottom: 30, marginTop: -10 },
  otpContainer: { flexDirection: 'row-reverse', justifyContent: 'space-between', width: '100%', marginBottom: 40 },
  otpBox: { width: 50, height: 60, borderWidth: 1, borderColor: '#007BFF', borderRadius: 8, textAlign: 'center', fontSize: 24, fontFamily: 'RubikBold', color: '#FFFFFF' },
  button: { backgroundColor: '#007BFF', paddingVertical: 18, width: '100%', borderRadius: 15, alignItems: 'center', marginTop: 20 },
  buttonDisabled: {
    backgroundColor: '#555',
  },
  buttonText: { fontFamily: 'GE_SS_Two_Bold', color: 'white', fontSize: 18 },
});