// components/CustomHeader.tsx
import { BlurView } from 'expo-blur';
import { Image } from 'expo-image';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';

import { DrawerActions } from '@react-navigation/native';
import { useNavigation } from 'expo-router';
export function CustomHeader() {
  const { top } = useSafeAreaInsets();
  // const colorScheme = useColorScheme(); // Removed unused variable
  const navigation = useNavigation(); // Get navigation object

  return (
    <BlurView
      // intensity controls the strength of the blur/frost effect
      intensity={10}
      // tint is crucial for Android and provides the base style on iOS
      tint= 'systemUltraThinMaterialDark'
      style={[styles.container, { paddingTop: top }]}
      experimentalBlurMethod='dimezisBlurView'
      blurReductionFactor={0.2}
    >
      <View style={styles.content}>
        <TouchableOpacity onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}>
        <Image
          source={require('@/assets/icons/menu.svg')}
          style={styles.menuIcon}
          contentFit="contain"
        />
        </TouchableOpacity>
        <View style={styles.logoContainer}>
          <ThemedText style={styles.title}>وليد زيتون</ThemedText>
          <Image
            source={require('@/assets/icons/logo.svg')}
            style={styles.logo}
            contentFit="contain"
          />
        </View>
      </View>
    </BlurView>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
    // Set background to transparent to allow the blur to be visible
  },
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    height: 60, // Increased height for better spacing
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  title: {
    // Corrected the font name to what we defined earlier
    fontFamily: 'GE_SS_Two_Bold',
    fontSize: 22, // Slightly larger for better readability
    color: '#ffffffff',
  },
  menuIcon: {
    width: 24,
    height: 24,
  },
  logo: {
    width: 40,
    height: 40,
  },
});