import { Image } from 'expo-image';
import React, { memo } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { ThemedText } from './ThemedText';
import { Link } from 'expo-router';

export const ContactSection = memo(() => {
  return (
    <View style={styles.container}>
      {/* Top Title */}
      <ThemedText style={styles.titleBlue}>لذلك!</ThemedText>
      <ThemedText style={styles.titleWhite}>
        ندعوكم لأول مكتب جماهيري{'\n'}لخدمة المواطنين
      </ThemedText>

      {/* App Mockup Image */}
      <Image
        source={require('@/assets/images/app-mockup.png')}
        style={styles.mockupImage}
        contentFit="contain"
        cachePolicy="disk"
      />

      {/* Bottom Text and Button */}
      <ThemedText style={styles.bottomText}>
        كسرنا حاجز المكان و الزمان{'\n'}و جئنا بمقرنا اليك!
      </ThemedText>
      <Link href="/(drawer)/(tabs)/register" asChild>
      <TouchableOpacity style={styles.button}>
        <ThemedText style={styles.buttonText}>سجل من هنا</ThemedText>
      </TouchableOpacity>
      </Link>
    </View>
  );
});
ContactSection.displayName = "ContactSection";

const styles = StyleSheet.create({
  container: {
    paddingVertical: 80,
    alignItems: 'center',
    backgroundColor: '#10141E', // App's dark background
  },
  titleBlue: {
    fontFamily: 'GE_SS_Text_Medium',
    fontSize: 42,
    color: '#007BFF',
  },
  titleWhite: {
    fontFamily: 'RubikBold',
    fontSize: 20,
    color: 'white',
    textAlign: 'center',
    marginTop: 15,
    lineHeight: 30,
  },
  mockupImage: {
    width: '90%',
    // A fixed height works well with contentFit="contain" to maintain aspect ratio
    height: 600,
    marginVertical: 40,
  },
  bottomText: {
    fontFamily: 'GE_SS_Text_Medium',
    fontSize: 22,
    color: 'white',
    textAlign: 'center',
    lineHeight: 32,
  },
  button: {
    backgroundColor: '#007BFF',
    paddingVertical: 18,
    width: '90%',
    borderRadius: 15,
    alignItems: 'center',
    marginTop: 30,
  },
  buttonText: {
    fontFamily: 'GE_SS_Text_Medium',
    color: 'white',
    fontSize: 24,
  },
});