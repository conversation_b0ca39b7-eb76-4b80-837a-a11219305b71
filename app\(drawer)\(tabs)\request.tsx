// /app/(drawer)/(tabs)/request.tsx
import { useAuth } from '@/context/AuthContext';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ImageBackground,
  KeyboardAvoidingView,
  KeyboardTypeOptions,
  Modal,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import Toast from 'react-native-toast-message'; // <-- IMPORT TOAST

const CITIES = [
  'كفر الشيخ (العاصمة)', 'دسوق', 'فوه', 'مطوبس', 'بلطيم', 'الحامول', 'بيلا', 'الرياض', 'سيدي سالم', 'قلين', 'برج البرلس',
];
const REQUEST_TYPES = ['خدمة عامة', 'شكوى', 'اقتراح', 'طلب توظيف'];

// --- Reusable Components
const LabeledInput = ({ label, placeholder, value, setValue, keyboardType }: {
  label: string;
  placeholder: string;
  value: string;
  setValue: (text: string) => void;
  keyboardType?: KeyboardTypeOptions;
}) => (
  <View style={styles.labeledInputContainer}>
    <ThemedText style={styles.label}>{label}</ThemedText>
    <View style={styles.inputWrapper}>
      <TextInput
        placeholder={placeholder}
        placeholderTextColor={Colors.dark.icon}
        style={styles.input}
        selectionColor={Colors.dark.tint}
        value={value}
        onChangeText={setValue}
        keyboardType={keyboardType}
      />
    </View>
  </View>
);

const LabeledTextArea = ({ label, placeholder, value, setValue }: {
  label: string;
  placeholder: string;
  value: string;
  setValue: (text: string) => void;
}) => (
  <View style={styles.labeledInputContainer}>
    <ThemedText style={styles.label}>{label}</ThemedText>
    <View style={[styles.inputWrapper, styles.textAreaWrapper]}>
      <TextInput
        placeholder={placeholder}
        placeholderTextColor={Colors.dark.icon}
        style={[styles.input, styles.textArea]}
        selectionColor={Colors.dark.tint}
        value={value}
        onChangeText={setValue}
        multiline
        maxLength={2000}
      />
    </View>
  </View>
);

// 2. LabeledPicker is updated with gesture fixes and the new prop
const LabeledPicker = ({ label, placeholder, items, selectedValue, onValueChange, showCloseButton = true }: {
  label: string;
  placeholder: string;
  items: string[];
  selectedValue: string;
  onValueChange: (value: string) => void;
  showCloseButton?: boolean; // New optional prop
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const translateY = useSharedValue(0);
  const context = useSharedValue({ y: 0 });

  const handleSelect = (item: string) => {
    onValueChange(item);
    closeModal();
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  useEffect(() => {
    if (modalVisible) {
      translateY.value = 0;
      context.value = { y: 0 };
    }
  }, [modalVisible, translateY, context]);

  const gesture = Gesture.Pan()
    .onStart(() => {
      context.value = { y: translateY.value };
    })
    .onUpdate((event) => {
      translateY.value = event.translationY + context.value.y;
      translateY.value = Math.max(translateY.value, 0);
    })
    .onEnd(() => {
      // FIX: Reduced threshold for a more responsive feel
      if (translateY.value > 100) {
        // Animate out then close
        translateY.value = withTiming(500, { duration: 250 }, () => {
          runOnJS(closeModal)();
        });
      } else {
        translateY.value = withSpring(0, { damping: 50 });
      }
    });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  return (
    <View style={styles.labeledInputContainer}>
      <ThemedText style={styles.label}>{label}</ThemedText>
      <TouchableOpacity style={[styles.inputWrapper, styles.pickerWrapper]} onPress={() => setModalVisible(true)}>
        <ThemedText style={selectedValue ? styles.pickerTextSelected : styles.pickerText}>
          {selectedValue || placeholder}
        </ThemedText>
        <Ionicons name="chevron-down" size={20} color={Colors.dark.icon} />
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={closeModal}>
        <TouchableOpacity style={styles.modalContainer} activeOpacity={1} onPressOut={closeModal}>
          <GestureDetector gesture={gesture}>
            <Animated.View style={[styles.modalContent, animatedStyle]}>
              <TouchableWithoutFeedback>
                <View>
                  <View style={styles.handleBar} />
                  <ScrollView>
                    {items.map((item) => (
                      <TouchableOpacity key={item} style={styles.modalItem} onPress={() => handleSelect(item)}>
                        <Text style={styles.modalItemText}>{item}</Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                  {/* FIX: The close button is now rendered conditionally */}
                  {showCloseButton && (
                    <TouchableOpacity style={styles.modalCloseButton} onPress={closeModal}>
                      <Text style={styles.modalCloseButtonText}>إغلاق</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </GestureDetector>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};


export default function RequestScreen() {
  const [name, setName] = useState('');
  const [center, setCenter] = useState('');
  const [address, setAddress] = useState('');
  const [phone, setPhone] = useState('');
  const [requestType, setRequestType] = useState('');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { authToken, user } = useAuth();

  useEffect(() => {
    // If the check is done and there's still no token, redirect.
    if (!authToken) {
      router.replace('/(drawer)/(tabs)/register');
    }
  }, [authToken, router]);


  const handleSubmit = async () => {
    if (!name || !center || !address || !phone || !requestType || !title || !description) {
      // Use error toast for validation
      Toast.show({
        type: 'error',
        text1: 'خطأ',
        text2: 'يرجى ملء جميع الحقول المطلوبة.',
      });
      return;
    }

    setIsLoading(true);

    const requestBody = {
      name: name,
      center: center,
      address: address,
      phone: phone,
      request_type: requestType,
      request_title: title,
      request_description: description,
    };

    try {
      const response = await fetch('https://api.waleedzaitoun.com/api/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const result = await response.json();

      if (response.ok) {
        // --- THIS IS THE KEY CHANGE ---
        // Use success toast instead of Alert
        Toast.show({
          type: 'success',
          text1: 'نجاح',
          text2: 'تم إرسال طلبك بنجاح.',
          position: 'top',
          visibilityTime: 4000,
        });
        // Clear the form
        setName(''); setCenter(''); setAddress(''); setPhone('');
        setRequestType(''); setTitle(''); setDescription('');
      } else {
        // Use error toast for API errors
        Toast.show({
          type: 'error',
          text1: 'خطأ في الإرسال',
          text2: result.message || 'حدث خطأ ما، يرجى المحاولة مرة أخرى.',
        });
      }
    } catch (error) {
      console.error('API Error:', error);
      // Use error toast for network errors
      Toast.show({
        type: 'error',
        text1: 'خطأ',
        text2: 'لا يمكن الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!authToken) {
    return null;
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <ImageBackground
          source={require('@/assets/backgrounds/hero-background.jpg')}
          style={styles.heroSection}>
          <LinearGradient
            colors={['transparent', 'rgba(16, 20, 30, 0.9)', '#10141E']}
            locations={[0.3, 0.7, 1]}
            style={styles.heroGradient}
          />
          <View style={styles.heroContent}>
            <ThemedText style={styles.heroTitle}>أهلاً بك {user?.name}</ThemedText>
            <ThemedText style={styles.heroSubtitle}>
              نحن هنا لخدمتكم, قم بارسال طلبك و سيتم التواصل معكم من أحد ممثلى خدمة الجماهير
            </ThemedText>
          </View>
        </ImageBackground>

        <View style={styles.formContainer}>
          <LabeledInput label="الاسم" placeholder="ادخل اسمك رباعى ...." value={name} setValue={setName} />
          <LabeledPicker label="المركز" placeholder="اختر المركز أو المدينة..." items={CITIES} selectedValue={center} onValueChange={setCenter} />
          <LabeledInput label="العنوان" placeholder="ادخل عنوانك بالتفصيل ...." value={address} setValue={setAddress} />
          <LabeledInput label="رقم الهاتف" placeholder="مثال : 01023456789" value={phone} setValue={setPhone} keyboardType="phone-pad" />
          <LabeledPicker label="نوع الطلب" placeholder="حدد نوع الطلب...." items={REQUEST_TYPES} selectedValue={requestType} onValueChange={setRequestType} />
          <LabeledInput label="عنوان الطلب" placeholder="ضع عنوان للطلب..." value={title} setValue={setTitle} />
          <LabeledTextArea label="وصف الطلب" placeholder="ضع وصف للطلب..." value={description} setValue={setDescription} />

          <TouchableOpacity style={styles.button} onPress={handleSubmit} disabled={isLoading}>
            <ThemedText style={styles.buttonText}>{isLoading ? 'جاري الإرسال...' : 'ارسال الطلب'}</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.whatsappButton}>
            <ThemedText style={styles.buttonText}>متابعة على الواتساب</ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

// Styles remain unchanged
const styles = StyleSheet.create({ container: { flex: 1, backgroundColor: Colors.dark.background, }, scrollContainer: { flexGrow: 1, }, heroSection: { height: 700, justifyContent: 'flex-end', alignItems: 'center', }, heroGradient: { ...StyleSheet.absoluteFillObject, }, heroContent: { alignItems: 'center', paddingHorizontal: 20, paddingBottom: 60, }, heroTitle: { fontFamily: 'RubikBold', fontSize: 28, color: '#FFFFFF', lineHeight: 40, textAlign: 'center', }, heroSubtitle: { fontFamily: 'GESSTwo', fontSize: 17, color: '#FFFFFF', textAlign: 'center', lineHeight: 28, marginTop: 15, opacity: 0.9, }, formContainer: { padding: 20, backgroundColor: Colors.dark.background, paddingBottom: 40, }, labeledInputContainer: { width: '100%', marginBottom: 25, }, label: { fontFamily: 'GE_SS_Two_Bold', fontSize: 16, color: '#007BFF', textAlign: 'right', marginBottom: 10, }, inputWrapper: { backgroundColor: 'transparent', borderColor: '#007BFF', borderWidth: 1, borderRadius: 12, minHeight: 65, justifyContent: 'center', paddingHorizontal: 15, }, input: { color: '#FFFFFF', fontFamily: 'GESSTwo', fontSize: 16, textAlign: 'right', width: '100%', paddingVertical: 0, }, pickerWrapper: { flexDirection: 'row-reverse', justifyContent: 'space-between', alignItems: 'center', height: 65, }, pickerText: { color: Colors.dark.icon, fontFamily: 'GESSTwo', fontSize: 16, textAlign: 'right', }, pickerTextSelected: { color: '#FFFFFF', fontFamily: 'GESSTwo', fontSize: 16, textAlign: 'right', }, textAreaWrapper: { paddingVertical: 10, }, textArea: { textAlignVertical: 'top', }, button: { backgroundColor: '#007BFF', paddingVertical: 18, width: '100%', borderRadius: 15, alignItems: 'center', }, whatsappButton: { backgroundColor: 'transparent', borderColor: '#007BFF', borderWidth: 1, paddingVertical: 18, width: '100%', borderRadius: 15, alignItems: 'center', marginTop: 20, }, buttonText: { fontFamily: 'GE_SS_Two_Bold', color: 'white', fontSize: 18, }, modalContainer: { flex: 1, justifyContent: 'flex-end', backgroundColor: 'rgba(0, 0, 0, 0.7)', }, modalContent: { backgroundColor: '#1E232C', borderTopLeftRadius: 20, borderTopRightRadius: 20, padding: 20, paddingTop: 10, maxHeight: '60%', }, handleBar: { width: 40, height: 5, borderRadius: 2.5, backgroundColor: '#4E545C', alignSelf: 'center', marginBottom: 15, }, modalItem: { paddingVertical: 15, borderBottomWidth: 1, borderBottomColor: '#333A45', }, modalItemText: { color: '#FFFFFF', fontSize: 18, textAlign: 'center', fontFamily: 'GESSTwo', }, modalCloseButton: { backgroundColor: '#007BFF', borderRadius: 12, padding: 15, marginTop: 20, }, modalCloseButtonText: { color: 'white', fontSize: 18, textAlign: 'center', fontFamily: 'GE_SS_Two_Bold', }, });