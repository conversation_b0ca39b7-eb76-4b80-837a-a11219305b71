// /components/CustomDrawerContent.tsx
import { Ionicons } from '@expo/vector-icons';
import { DrawerContentScrollView } from '@react-navigation/drawer';
import { DrawerActions } from '@react-navigation/native';
import { Link } from 'expo-router';
import { Image, Linking, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { Colors } from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

const DRAWER_ITEMS = [
  { name: 'الرئيسية', icon: 'home-outline', target: 'index' },
  { name: 'الاخبار', icon: 'newspaper-outline', target: 'news' },
];

export function CustomDrawerContent(props) {
  const { top, bottom } = useSafeAreaInsets();
  const { navigation } = props;
  const { authToken, user, signOut, isSessionValidated } = useAuth();

  const handleNavigate = (target: string, isTab: boolean = true) => {
    if (isTab) {
      navigation.navigate('(tabs)', { screen: target });
    } else {
      navigation.navigate(target);
    }
    navigation.dispatch(DrawerActions.closeDrawer());
  };

  const handleLogout = async () => {
    await signOut();
    navigation.dispatch(DrawerActions.closeDrawer());
    navigation.navigate('(tabs)', { screen: 'index' });
  };

  const handleLinkPress = (url: string) => {
    Linking.openURL(url).catch(err => console.error("Couldn't load page", err));
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={[styles.header, { paddingTop: top + 20 }]}>
        {authToken && user ? (
          <ThemedText style={styles.title}>{`أهلاً بك\n${user.name}`}</ThemedText>
          
        ) : (
          <ThemedText style={styles.title}>مرحبا بك</ThemedText>
        )}
      </View>
      

      {/* Scrollable Content */}
      <DrawerContentScrollView
        {...props}
        contentContainerStyle={styles.scrollContainer}
        style={styles.scrollView}>

        {/* --- ADD THE PROFILE LINK HERE --- */}
        {isSessionValidated && authToken && user && (
          <TouchableOpacity style={styles.drawerItem} onPress={() => handleNavigate('profile', false)}>
            <Text style={styles.drawerLabel}>الملف الشخصي</Text>
            <Ionicons name="person-circle-outline" size={22} color={Colors.dark.icon} />
          </TouchableOpacity>
        )}
        
        {/* Navigation Items */}
        {DRAWER_ITEMS.map((item) => (
          <TouchableOpacity key={item.name} style={styles.drawerItem} onPress={() => handleNavigate(item.target)}>
            <Text style={styles.drawerLabel}>{item.name}</Text>
            <Ionicons name={item.icon as any} size={22} color={Colors.dark.icon} />
          </TouchableOpacity>
        ))}

        

        {/* Only render this block AFTER the session has been validated */}
        {isSessionValidated && authToken && user && (
          <>
            {user.role === 'admin' ? (
              <TouchableOpacity style={styles.drawerItem} onPress={() => handleNavigate('dashboard', false)}>
                <Text style={styles.drawerLabel}>لوحة التحكم</Text>
                <Ionicons name="grid-outline" size={22} color={Colors.dark.icon} />
              </TouchableOpacity>
            ) : (
              <TouchableOpacity style={styles.drawerItem} onPress={() => handleNavigate('my-requests', false)}>
                <Text style={styles.drawerLabel}>طلباتي</Text>
                <Ionicons name="file-tray-full-outline" size={22} color={Colors.dark.icon} />
              </TouchableOpacity>
            )}
          </>
        )}

        {/* Dynamic Auth Item (Register/Logout) */}
        {authToken ? (
          <TouchableOpacity style={styles.drawerItem} onPress={handleLogout}>
            <Text style={styles.drawerLabel}>تسجيل الخروج</Text>
            <Ionicons name="log-out-outline" size={22} color={Colors.dark.icon} />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity style={styles.drawerItem} onPress={() => handleNavigate('register', true)}>
            <Text style={styles.drawerLabel}>التسجيل</Text>
            <Ionicons name="person-add-outline" size={22} color={Colors.dark.icon} />
          </TouchableOpacity>
        )}


        
        {/* Request Button (This remains for all logged-in users) */}
      {authToken && (
        <View style={styles.requestSection}>
          <Link href="/(drawer)/(tabs)/request" asChild>
            <TouchableOpacity style={styles.requestButton} onPress={() => navigation.dispatch(DrawerActions.closeDrawer())}>
              <ThemedText style={styles.requestButtonText}>تقديم طلب</ThemedText>
              <Ionicons name="document-text-outline" size={20} color="white" />
            </TouchableOpacity>
          </Link>
        </View>
      )}

        {/* Social Section */}
        <View style={styles.socialSection}>
          <Image source={require('@/assets/icons/logo.svg')} style={styles.socialLogo} />
          <ThemedText style={styles.socialTitle}>تابعنا على مواقع التواصل الاجتماعي</ThemedText>
          <View style={styles.socialIconsContainer}>
            <TouchableOpacity onPress={() => handleLinkPress('https://www.facebook.com/walidzaytoonofficial')}>
              <View style={styles.iconWrapper}>
                <Ionicons name="logo-facebook" size={30} color={Colors.dark.background} />
              </View>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => handleLinkPress('https://www.instagram.com/waleed_zaytoun')}>
              <View style={styles.iconWrapper}>
                <Ionicons name="logo-instagram" size={30} color={Colors.dark.background} />
              </View>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => handleLinkPress('https://wa.me/201065444720')}>
               <View style={styles.iconWrapper}>
                <Ionicons name="logo-whatsapp" size={30} color={Colors.dark.background} />
              </View>
            </TouchableOpacity>
          </View>
        </View>

      </DrawerContentScrollView>

      {/* Footer */}
      <TouchableOpacity onPress={() => handleLinkPress('https://api.whatsapp.com/send/?phone=201000094180&text&type=phone_number&app_absent=0')}>
        <View style={[styles.footer, { paddingBottom: bottom + 10 }]}>
          <ThemedText style={styles.creditText}>تصميم وبرمجة</ThemedText>
          <ThemedText style={styles.creditTextBold}>فريق عمل رفيق أكاديمي</ThemedText>
        </View>
      </TouchableOpacity>
    </ThemedView>
  );
}

// Styles remain unchanged
const styles = StyleSheet.create({
  container: { flex: 1 },
  header: { 
    paddingBottom: 15, 
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  title: { 
    fontFamily: 'GE_SS_Two_Bold', 
    fontSize: 22,
    color: '#FFFFFF', 
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContainer: {
    paddingTop: 10,
  },
  drawerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingVertical: 9,
    paddingHorizontal: 20,
    marginHorizontal: 10,
    gap: 15,
  },
  drawerLabel: {
    fontFamily: 'GESSTwo',
    fontSize: 20,
    color: Colors.dark.text,
  },
  socialSection: {
    alignItems: 'center',
  },
  socialLogo: {
    width: 50,
    height: 50,
    marginBottom: 15,
  },
  socialTitle: {
    fontFamily: 'GE_SS_Two_Bold',
    fontSize: 18,
    color: '#FFFFFF',
    marginBottom: 20,
    textAlign: 'center',
  },
  socialIconsContainer: {
    flexDirection: 'row-reverse',
    gap: 30,
  },
  iconWrapper: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  requestSection: { padding: 20 },
  requestButton: { backgroundColor: '#007BFF', flexDirection: 'row', alignItems: 'center', justifyContent: 'center', padding: 15, borderRadius: 12, gap: 12 },
  requestButtonText: { fontFamily: 'GE_SS_Two_Bold', fontSize: 16, color: '#FFFFFF' },
  footer: {
    alignItems: 'center',
    paddingVertical: 10,
    borderTopColor: '#333',
    borderTopWidth: 1,
  },
  creditText: {
    fontFamily: 'GESSTwoLight',
    fontSize: 16,
    color: '#9BA1A6',
    lineHeight: 24,
  },
  creditTextBold: {
    fontFamily: 'GE_SS_Two_Bold',
    fontSize: 16,
    color: '#007BFF',
    textDecorationLine: 'underline',
  },
});