import React from 'react';
import {
    Dimensions,
    ImageBackground,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { ThemedText } from './ThemedText';
import { Link } from 'expo-router';

const { height } = Dimensions.get('window');

export function HeroSection() {
  return (
    <ImageBackground
      // NOTE: Add your background image to the assets/images directory
      source={require('@/assets/backgrounds/hero-background.jpg')}
      style={styles.heroBackground}
      resizeMode="cover">
      <View style={styles.overlay}>
        <ThemedText style={styles.welcomeText}>
          أهــلاً بـــكــم فـى الـتطـبـيــق الرســمـــى لـرجــل الأعــمـــال
        </ThemedText>
        <ThemedText style={styles.nameText}>وليد محمد زيتون</ThemedText>
        <Link href="/(drawer)/request" asChild>
        <TouchableOpacity style={styles.button}>
          <Text style={styles.buttonText}>هنا لخدمتكم</Text>
        </TouchableOpacity>
        </Link>
      </View>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  heroBackground: {
    width: '100%',
    height: height,
    justifyContent: 'flex-end',
    alignItems: 'center',
    
  },
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    padding: 10,
    paddingTop: "110%",
  },
  welcomeText: {
    fontFamily: 'GESSTwo',
    fontSize: 20,
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },
  nameText: {
    fontFamily: 'RubikBold',
    fontSize: 32,
    color: 'white',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 40,
  },
  button: {
    backgroundColor: '#007BFF',
    paddingVertical: 12,
    paddingHorizontal: 50,
    borderRadius: 20,
  },
  buttonText: {
    fontFamily: 'rabi3',
    color: 'white',
    fontSize: 24,
  },
});