import React from 'react';
import { FlatList, StyleSheet } from 'react-native';

import { AboutSection } from '@/components/AboutSection';
import { BiographySection } from '@/components/BiographySection';
import { ExperienceSection } from '@/components/ExperienceSection';
import { HeroSection } from '@/components/HeroSection';
import { CampaignSection } from '@/components/CampaignSection';
import { ContactSection } from '@/components/ContactSection';
import { CountdownSection } from '@/components/CountdownSection';
import { MottoSection } from '@/components/MottoSection';
import { NewsSection } from '@/components/NewsSection';

// 1. Define an array of your sections.
// Each object has a unique key and the component to render.
const sections = [
  { key: 'hero', component: <HeroSection /> },
  { key: 'about', component: <AboutSection /> },
  { key: 'biography', component: <BiographySection /> },
  { key: 'experience', component: <ExperienceSection /> },
  { key: 'campaign', component: <CampaignSection /> },
  { key: 'countdown', component: <CountdownSection /> },
  { key: 'motto', component: <MottoSection /> },
  { key: 'contact', component: <ContactSection /> },
  { key: 'news', component: <NewsSection /> },
  // You can add more sections here in the future
];

export default function HomeScreen() {
  // 2. Define a function to render each item in the list.
  // This function simply returns the component for the given item.
  const renderItem = ({ item }: { item: { key: string; component: React.ReactElement } }) => {
    return item.component;
  };

  return (
    // 3. Use the FlatList component instead of ScrollView.
    <FlatList
      style={styles.container}
      data={sections}
      renderItem={renderItem}
      keyExtractor={(item) => item.key}
      // These props are important for performance:
      initialNumToRender={2} // Renders the first 2 items on initial load.
      maxToRenderPerBatch={1} // Renders 1 new item per batch as you scroll.
      windowSize={3} // Renders the visible screen + one screen above and one below.
    />
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // The background color is now correctly handled by your global theme.
  },
});