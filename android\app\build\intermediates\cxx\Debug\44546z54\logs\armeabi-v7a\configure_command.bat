@echo off
"C:\\SDK\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HD:\\dev\\Zayton\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=armeabi-v7a" ^
  "-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a" ^
  "-DANDROID_NDK=C:\\SDK\\ndk\\27.1.12297006" ^
  "-DCMAKE_ANDROID_NDK=C:\\SDK\\ndk\\27.1.12297006" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\SDK\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=C:\\SDK\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\dev\\Zayton\\android\\app\\build\\intermediates\\cxx\\Debug\\44546z54\\obj\\armeabi-v7a" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\dev\\Zayton\\android\\app\\build\\intermediates\\cxx\\Debug\\44546z54\\obj\\armeabi-v7a" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-DCMAKE_FIND_ROOT_PATH=D:\\dev\\Zayton\\android\\app\\.cxx\\Debug\\44546z54\\prefab\\armeabi-v7a\\prefab" ^
  "-BD:\\dev\\Zayton\\android\\app\\.cxx\\Debug\\44546z54\\armeabi-v7a" ^
  -GNinja ^
  "-DPROJECT_BUILD_DIR=D:\\dev\\Zayton\\android\\app\\build" ^
  "-DPROJECT_ROOT_DIR=D:\\dev\\Zayton\\android" ^
  "-DREACT_ANDROID_DIR=D:\\dev\\Zayton\\node_modules\\react-native\\ReactAndroid" ^
  "-DANDROID_STL=c++_shared" ^
  "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"
