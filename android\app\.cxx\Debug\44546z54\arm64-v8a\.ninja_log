# ninja log v5
46385	60221	7752360767327832	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/917f708e242e4d8be12911227a568149/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	f30d325cf15cc464
3	145	0	D:/dev/Zayton/android/app/.cxx/Debug/44546z54/arm64-v8a/CMakeFiles/cmake.verify_globs	dd72a501a20c9170
157	14773	7752360312937827	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	78d73e1725329d
17618	36439	7752360529257779	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	c54348e42376eac7
36440	55365	7752360718587830	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/037303a3626942774a233183d6d53c2b/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	d33e88fccf7b4035
77041	90401	7752361067937779	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/910b561b42ee9ec61144ee4f3e7c7162/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	bbc1ee90afc8f276
173	18022	7752360345267805	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	9d00c169fafa9031
68265	83077	7752360995827784	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o	1dde97442db9f164
76701	93610	7752361101107809	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/26fefe7d2ba27d8025af95104418f116/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	3c0f098094ec39f4
47592	61211	7752360776167800	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/917f708e242e4d8be12911227a568149/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o	6b8eef31c1b72f1d
97	23215	7752360397177796	CMakeFiles/appmodules.dir/OnLoad.cpp.o	ed349a5934b30e7d
127	23242	7752360397647792	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	44a26d5582e8dbb5
79919	100637	7752361170947805	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/26fefe7d2ba27d8025af95104418f116/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	6c73a0d903f4f8fc
143	20468	7752360369717808	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	4c554fe322ca20f5
199	22742	7752360392557793	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	fab81f085e496e25
318	20561	7752360370047820	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	3998fb5053aaf17f
49171	65703	7752360821847789	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/D_/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	d27951d5304e0d1c
112	67080	7752360832827809	CMakeFiles/appmodules.dir/D_/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	c8dacd11dd9c19f4
221	19562	7752360360087848	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	477ed007955f0a09
61403	63111	7752360793647824	D:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/arm64-v8a/libreact_codegen_safeareacontext.so	bc6830036c7441e3
20562	35082	7752360515227811	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	45aaff0a2916c33
409	17615	7752360341337816	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	c21da5ee0908033b
19564	30098	7752360465577815	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	21c9b8ea081b5670
20470	34822	7752360513347805	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	99744cc62e2c87b3
55366	68264	7752360847887793	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	2030be8419bd8fb7
100638	101880	7752361180307787	D:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/arm64-v8a/libreact_codegen_rnscreens.so	2e7b304d7f7c5289
18023	32141	7752360486057806	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	960be7b2f838e78d
23243	37161	7752360536557792	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	637f69ca09fe458d
48509	63992	7752360804247824	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	ccedab670247907c
14777	35512	7752360519327815	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	9daf87c81de7f2b4
95674	109525	7752361259707799	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/c01f95d0ffa395a187816a33968304b9/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o	416d3376a97f8c33
23216	37325	7752360536877798	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	1359c9d8cd6bea21
22745	33783	7752360502827844	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	ef50917791c6488f
60222	76453	7752360929287826	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	22d72beb22a83272
34823	44766	7752360612237817	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	db9e934a07b3dd0d
113387	113889	7752361303657822	D:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/arm64-v8a/libreact_codegen_rnsvg.so	76d382dc89059cd1
32142	45052	7752360615317819	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	13e313fc8c92960
35083	46384	7752360628817815	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	74cf86c9fc30d0fb
33784	47886	7752360643917809	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	ab4a3a26e4483377
44767	56838	7752360733047809	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4060490531b5fb00f72034607561b9fc/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o	b9abd342e3fbb0e3
37539	47590	7752360640237835	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3ab5fe48a452a57eb5718883907c7eba/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o	553b5ff914d33288
65704	76383	7752360928587851	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o	8b7973c5794ba3d7
37162	49169	7752360656947807	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3ab5fe48a452a57eb5718883907c7eba/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o	8249693d9918c846
65130	82370	7752360988817843	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o	df89cc0faa6112a2
90715	104581	7752361210617828	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o	3c0415fd81ebd56e
30099	48507	7752360649837794	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8a26c049427bcc82f1d830398b2cfb87/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	dbaf981c408e8ea5
35513	50939	7752360673807820	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3ab5fe48a452a57eb5718883907c7eba/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	fcdd1ad496016858
117547	117967	7752361344528166	D:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/arm64-v8a/libappmodules.so	ee0353ad49092598
76384	95610	7752361119967821	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	81624ef0d85a073b
74680	91650	7752361081357817	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o	f389b1bb8c4e6d00
45053	61402	7752360778587782	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3ab5fe48a452a57eb5718883907c7eba/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o	d5fa5cb64455d960
47887	65128	7752360815957804	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/D_/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	b68b8cee37c2ba2c
67081	79917	7752360963857847	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o	7fe690f89bde0bcc
79265	89986	7752361064947777	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	ea4a083e396b6e7b
50940	63792	7752360802517807	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/D_/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	6ad55a48af433535
61213	77000	7752360934417781	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	21ce735777ffc665
56840	69993	7752360864217824	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	c885c7f1c6ed9e6
63993	74679	7752360911257840	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/c01f95d0ffa395a187816a33968304b9/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o	41713e4915735f65
63112	79264	7752360957827811	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	8d9bb4fcbd84028d
63947	88635	7752361050697828	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/910b561b42ee9ec61144ee4f3e7c7162/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	646761735846bdf9
92552	107713	7752361242167804	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/c01f95d0ffa395a187816a33968304b9/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o	c6ea5e5f5bf1ce5b
70178	89706	7752361061117834	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/c01f95d0ffa395a187816a33968304b9/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o	6636d2f84fab441d
99711	113166	7752361296707801	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o	775c2943f0f6d244
93611	103065	7752361195467819	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o	70372807078bc1e3
91651	113385	7752361298307811	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o	3bc7c4b23aede82e
82371	92550	7752361090717812	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o	e145b27bc6a3bba1
89987	105560	7752361220697958	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o	53aa8250b9c89bd4
83078	99710	7752361161817782	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o	2c9b6c00c6d53315
103066	114755	7752361312717828	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	e64d9180fbe0db2a
107374	117547	7752361340878624	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	1b205f99fb581ba5
106404	114860	7752361313947790	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	1ffac7d957a25c91
104678	115927	7752361324618193	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	15b8c9d4790f2ba
107715	117168	7752361337018156	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	7b2246de9a27e136
89707	106403	7752361228977818	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o	d5a9471aedaf8d6f
88636	107373	7752361238627803	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o	486ce23e45fbb133
101881	112136	7752361286277822	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	4ef4c7557a71a254
105561	116652	7752361331678167	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	5b74c1952c710a05
0	29	0	clean	edc044b4fa31ef3a
146	2525	7753114619516117	build.ninja	a9ee86b5a3a83ebc
1	44	0	D:/dev/Zayton/android/app/.cxx/Debug/44546z54/arm64-v8a/CMakeFiles/cmake.verify_globs	dd72a501a20c9170
135	18046	7753114803966150	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o	aa546f6dfa13a66d
100	19245	7753114815746163	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o	a4eac9b4d8238216
76	19778	7753114821146169	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o	b2b4c875c30811c0
66	20095	7753114824386125	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o	d36da68d4b93d2ae
87	21913	7753114842686185	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o	619d30521ad36e21
39	22267	7753114846186127	CMakeFiles/appmodules.dir/OnLoad.cpp.o	348310c26d6a9f5b
111	23361	7753114857026173	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o	42a6e471d2b1357d
124	24343	7753114866916105	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o	df4524238d31661f
57	39493	7753115017156504	CMakeFiles/appmodules.dir/D_/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	89c0821596c35d65
39494	40530	7753115028383603	D:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/arm64-v8a/libappmodules.so	71bcb6aae1913efb
