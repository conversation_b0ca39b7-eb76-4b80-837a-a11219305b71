import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient'; // <-- 1. Import the component
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

import { ThemedText } from './ThemedText';

export function ExperienceSection() {
  return (
    <View style={styles.container}>
      {/* The background image remains at the bottom of the stack */}
      <Image
        source={require('@/assets/backgrounds/experience-background.png')}
        style={styles.backgroundImage}
        contentFit="cover"
      />

      {/* 2. Add the LinearGradient component on top of the image */}
      <LinearGradient
        // Starts with the background color at 100% opacity (top)
        // and fades to 0% opacity (bottom), revealing the image.
        colors={['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 1)']}
        locations={[0.1, 0.5]}
        style={styles.gradient}
        
        // You can also control the direction, e.g., start={{ x: 0, y: 0 }} end={{ x: 1, y: 1 }}
      />

      {/* The text content sits on top of everything */}
      <View style={styles.contentContainer}>
        {/* Political Experience Section (No changes here) */}
        <View style={styles.section1}>
            <ThemedText style={styles.sectionTitle}>الخبرات السياسية</ThemedText>
            <View style={styles.list}>
              <View style={styles.listItemContainer}>
                <ThemedText style={styles.bullet}>•</ThemedText>
                <ThemedText style={styles.listItemText}>
                  <Text style={styles.boldText}>أمين مساعد </Text>
                  لحزب مستقبل وطن محافظة كفر الشيخ.
                </ThemedText>
              </View>
              <View style={styles.listItemContainer}>
                <ThemedText style={styles.bullet}>•</ThemedText>
                <ThemedText style={styles.listItemText}>
                  <Text style={styles.boldText}>أمين شباب </Text>
                  بمنطقة الساحل البحري لمدة سنتين.
                </ThemedText>
              </View>
              <View style={styles.listItemContainer}>
                <ThemedText style={styles.bullet}>•</ThemedText>
                <ThemedText style={styles.listItemText}>
                  <Text style={styles.boldText}>عضو لجنة الثلاثين </Text>
                  على مستوى المركز. عضو المجلس المحلي الشعبي لمحافظة كفر الشيخ (دورة ٢٠٠٨-٢٠١٢).
                </ThemedText>
              </View>
              <View style={styles.listItemContainer}>
                <ThemedText style={styles.bullet}>•</ThemedText>
                <ThemedText style={styles.listItemText}>
                  والخدمة الاجتماعية بحزب مستقبل
                  <Text style={styles.boldText}> ٧ </Text>
                  وطن - كفر الشيخ سابقاً.
                </ThemedText>
              </View>
            </View>
          </View>

        {/* Work Experience Section (No changes here) */}
        <View style={styles.section2}>
          <ThemedText style={styles.sectionTitle}>الخبرات العملية</ThemedText>
          <View style={styles.list}>
            <View style={styles.listItemContainer}>
              <ThemedText style={styles.bullet}>•</ThemedText>
              <ThemedText style={styles.boldText}>
                شريك مؤسس ومساهم
                في عدة كيانات اقتصادية ناجحة أبرزها:
              </ThemedText>
            </View>
            <View style={styles.listItemContainer}>
              <ThemedText style={styles.bullet}>•</ThemedText>
              <ThemedText style={styles.listItemText}>شركة دلتا كابيتال للتطوير العقاري.</ThemedText>
            </View>
            <View style={styles.listItemContainer}>
              <ThemedText style={styles.bullet}>•</ThemedText>
              <ThemedText style={styles.listItemText}>شركة سينيرجي – المسؤولة عن تنفيذ:</ThemedText>
            </View>
            <View style={styles.subList}>
              <ThemedText style={styles.subListItemText}>١. قرية مارينا دلتا السياحية</ThemedText>
              <ThemedText style={styles.subListItemText}>٢. قرية مارينا لاجونز السياحية</ThemedText>
              <ThemedText style={styles.subListItemText}>٣. قرية بلو بيتش السياحية</ThemedText>
            </View>
            <View style={styles.listItemContainer}>
              <ThemedText style={styles.bullet}>•</ThemedText>
              <ThemedText style={styles.listItemText}>شركة الزيتون أوتو مول</ThemedText>
            </View>
            <View style={styles.listItemContainer}>
              <ThemedText style={styles.bullet}>•</ThemedText>
              <ThemedText style={styles.listItemText}>شركة دلتا كوست</ThemedText>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative', // Allows absolute positioning for children (the image)
  },
  backgroundImage: {
    ...StyleSheet.absoluteFillObject, // Makes the image fill the container view
    // --- YOU CAN MODIFY THE IMAGE HERE ---
     width: '100%',   // Example: Zoom the image in
     height: '60%',  // Example: Stretch it vertically
     top: 20,        // Example: Move it up
  },
  gradient: {
    // 3. This new style makes the gradient cover the entire container.
    ...StyleSheet.absoluteFillObject,
  },
  contentContainer: {
    // This view now only worries about padding for the text.
    // It's transparent so the image behind it is visible.
    paddingVertical: 100,
    paddingHorizontal: 20,
    backgroundColor: 'transparent',
  },
  // --- All the styles below are the same as before ---
  section1: {
    paddingTop: 120,
    marginTop: 200,
    width: '100%',
    alignItems: 'flex-end',
  },
  section2: {
    marginTop: 100,
    width: '100%',
    alignItems: 'flex-end',
  },
  sectionTitle: {
    fontFamily: 'GE_SS_Two_Bold',
    fontSize: 27,
    color: '#007BFF',
    textAlign: 'right',
    marginBottom: 20,
    writingDirection: 'rtl',
    lineHeight: 30,
  },
  list: {
    alignItems: 'flex-end',
    gap: 5,
  },
  listItemContainer: {
    flexDirection: 'row-reverse',
    width: '100%',
    alignItems: 'flex-start',
    gap: 7,
  },
  bullet: {
    fontFamily: 'GESSTwo',
    fontSize: 20,
    color: 'white',
    marginRight: 10,
    writingDirection: 'rtl',
    lineHeight: 20,
    paddingRight: 4,
    marginTop: 5,
  },
  listItemText: {
    flex: 1,
    fontFamily: 'GESSTwo',
    fontSize: 15,
    color: 'white',
    textAlign: 'right',
    writingDirection: 'rtl',
  },
  boldText: {
    fontFamily: 'GE_SS_Two_Bold',
    fontSize: 15,
    color: 'white',
    textAlign: 'right',
    writingDirection: 'rtl',
  },
  subList: {
    alignItems: 'flex-end',
    width: '85%',
    gap: 2,
    
  },
  subListItemText: {
    fontFamily: 'GESSTwo',
    fontSize: 15,
    color: 'white',
    textAlign: 'right',
    writingDirection: 'rtl',
  },
});