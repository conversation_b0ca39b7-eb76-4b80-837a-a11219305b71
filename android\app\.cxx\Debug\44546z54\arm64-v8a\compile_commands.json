[{"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -ID:/dev/Zayton/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\D_\\dev\\Zayton\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c D:\\dev\\Zayton\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "D:\\dev\\Zayton\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -ID:/dev/Zayton/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp", "file": "D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp", "file": "D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp", "file": "D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp", "file": "D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp", "file": "D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleSignInCGen_autolinked_build\\CMakeFiles\\react_codegen_RNGoogleSignInCGen.dir\\RNGoogleSignInCGen-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\RNGoogleSignInCGen-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\RNGoogleSignInCGen-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleSignInCGen_autolinked_build\\CMakeFiles\\react_codegen_RNGoogleSignInCGen.dir\\react\\renderer\\components\\RNGoogleSignInCGen\\ComponentDescriptors.cpp.o -c D:\\dev\\Zayton\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleSignInCGen\\ComponentDescriptors.cpp", "file": "D:\\dev\\Zayton\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleSignInCGen\\ComponentDescriptors.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleSignInCGen_autolinked_build\\CMakeFiles\\react_codegen_RNGoogleSignInCGen.dir\\react\\renderer\\components\\RNGoogleSignInCGen\\EventEmitters.cpp.o -c D:\\dev\\Zayton\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleSignInCGen\\EventEmitters.cpp", "file": "D:\\dev\\Zayton\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleSignInCGen\\EventEmitters.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleSignInCGen_autolinked_build\\CMakeFiles\\react_codegen_RNGoogleSignInCGen.dir\\react\\renderer\\components\\RNGoogleSignInCGen\\Props.cpp.o -c D:\\dev\\Zayton\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleSignInCGen\\Props.cpp", "file": "D:\\dev\\Zayton\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleSignInCGen\\Props.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleSignInCGen_autolinked_build\\CMakeFiles\\react_codegen_RNGoogleSignInCGen.dir\\react\\renderer\\components\\RNGoogleSignInCGen\\RNGoogleSignInCGenJSI-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleSignInCGen\\RNGoogleSignInCGenJSI-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleSignInCGen\\RNGoogleSignInCGenJSI-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleSignInCGen_autolinked_build\\CMakeFiles\\react_codegen_RNGoogleSignInCGen.dir\\react\\renderer\\components\\RNGoogleSignInCGen\\ShadowNodes.cpp.o -c D:\\dev\\Zayton\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleSignInCGen\\ShadowNodes.cpp", "file": "D:\\dev\\Zayton\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleSignInCGen\\ShadowNodes.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleSignInCGen_autolinked_build\\CMakeFiles\\react_codegen_RNGoogleSignInCGen.dir\\react\\renderer\\components\\RNGoogleSignInCGen\\States.cpp.o -c D:\\dev\\Zayton\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleSignInCGen\\States.cpp", "file": "D:\\dev\\Zayton\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleSignInCGen\\States.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\rngesturehandler_codegen-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\Props.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\States.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\rnreanimated-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\8a26c049427bcc82f1d830398b2cfb87\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\917f708e242e4d8be12911227a568149\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\037303a3626942774a233183d6d53c2b\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\3ab5fe48a452a57eb5718883907c7eba\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\3ab5fe48a452a57eb5718883907c7eba\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\3ab5fe48a452a57eb5718883907c7eba\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\3ab5fe48a452a57eb5718883907c7eba\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\4060490531b5fb00f72034607561b9fc\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\917f708e242e4d8be12911227a568149\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6e0e1aab7bcc62b53b2923a817264dd7\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\D_\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\D_\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6e0e1aab7bcc62b53b2923a817264dd7\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6e0e1aab7bcc62b53b2923a817264dd7\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6e0e1aab7bcc62b53b2923a817264dd7\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6e0e1aab7bcc62b53b2923a817264dd7\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\D_\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\910b561b42ee9ec61144ee4f3e7c7162\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\26fefe7d2ba27d8025af95104418f116\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6e0e1aab7bcc62b53b2923a817264dd7\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\26fefe7d2ba27d8025af95104418f116\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6e0e1aab7bcc62b53b2923a817264dd7\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\910b561b42ee9ec61144ee4f3e7c7162\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\D_\\dev\\Zayton\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\D_\\dev\\Zayton\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\D_\\dev\\Zayton\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\D_\\dev\\Zayton\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\rnsvg.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\c01f95d0ffa395a187816a33968304b9\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\c01f95d0ffa395a187816a33968304b9\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\D_\\dev\\Zayton\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\c01f95d0ffa395a187816a33968304b9\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\D_\\dev\\Zayton\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\c01f95d0ffa395a187816a33968304b9\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\RNCWebViewSpec-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\RNEdgeToEdge-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\RNEdgeToEdge-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\RNEdgeToEdge-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp"}, {"directory": "D:/dev/Z<PERSON>ton/android/app/.cxx/Debug/44546z54/arm64-v8a", "command": "C:\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/SDK/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp.o -c D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp", "file": "D:\\dev\\Zayton\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp"}]