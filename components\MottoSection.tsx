import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import React, { memo } from 'react';
import { StyleSheet, View } from 'react-native';

import { ThemedText } from './ThemedText';

export const MottoSection = memo(() => {
  return (
    <View style={styles.container}>
      {/* Top Title */}
      <View style={styles.titleContainer}>
        <ThemedText style={styles.titleBlue}>خير الناس</ThemedText>
        <ThemedText style={styles.titleWhite}>أنفعهم للناس</ThemedText>
      </View>

      {/* Image and Text Overlay Section */}
      <View style={styles.imageContainer}>
        {/* The background image, at the bottom of the stack */}
        <Image
          source={require('@/assets/backgrounds/about-background.png')}
          style={styles.backgroundImage}
          contentFit="cover"
          cachePolicy="disk"
        />

        {/* The gradient overlay, on top of the image */}
        <LinearGradient
          // Fades from semi-transparent to fully opaque, making text readable
          colors={['rgba(67, 67, 67, 0.15)', 'rgba(0, 0, 0, 1), 1)']}
          locations={[0.3, 0.9]} // Control how quickly the fade happens
          style={styles.gradientOverlay}
        />

        {/* The text, which sits on top of the image and gradient */}
        <View style={styles.textOverlay}>
          <ThemedText style={styles.mottoText}>
            ظل هذا الشعار رفيقاً في رحلتنا
            في العمل السياسي و المجتمعي
            {'\n\n'}
            حتى أصبح منهجاً إنسانياً و سعياً موصولاً
            نبتغى منه خدمة أهلنا ما استطعنا
            {'\n\n'}
            وفي ظل التطور التكنولوجي الحالي
            كان لابد أن نكون سباقين في دمج هذا
            التطور في عملنا الاجتماعي و السياسي

          </ThemedText>
        </View>
      </View>
    </View>
  );
});
MottoSection.displayName = "MottoSection";

const styles = StyleSheet.create({
  container: {
    paddingTop: 60,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  titleBlue: {
    fontFamily: 'GE_SS_Two_Bold',
    fontSize: 28,
    color: '#007BFF',
    lineHeight: 40,
    textAlign: 'center',
  },
  titleWhite: {
    fontFamily: 'RubikBold',
    fontSize: 30,
    color: 'white',
    marginTop: 5,
    lineHeight: 50,
    textAlign: 'center',
  },
  imageContainer: {
    height: 800, // Adjust height as needed
    width: '100%',
    position: 'relative', // Needed to contain the absolutely positioned children
    alignItems: 'center',
  },
  backgroundImage: {
    ...StyleSheet.absoluteFillObject,
    width: '110%',
    height: '170%',
  },
  gradientOverlay: {
    ...StyleSheet.absoluteFillObject,
  },
  textOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center', // Center the text vertically
    alignItems: 'center', // Center the text horizontally
    paddingTop: 270, // Push text down from the top slightly
    paddingHorizontal: 15, // Add some padding to the sides
  },
  mottoText: {
    fontFamily: 'GESSTwoLight',
    fontSize: 20,
    lineHeight: 25, // Good line spacing for readability
    color: 'white',
    textAlign: 'center',
    width: '100%', // Prevent text from touching the screen edges
  },
});