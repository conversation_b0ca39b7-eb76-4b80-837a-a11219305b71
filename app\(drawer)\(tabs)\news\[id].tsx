// /app/(drawer)/(tabs)/news/[id].tsx
import { useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  ImageBackground,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';

interface Article {
  title: string;
  description: string;
  image_path: string;
}

export default function NewsDetailScreen() {
  const { id } = useLocalSearchParams();
  const [article, setArticle] = useState<Article | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) return;

    const fetchArticle = async () => {
      try {
        const response = await fetch(`https://api.waleedzaitoun.com/api/news/${id}`);
        if (!response.ok) throw new Error('Failed to fetch article');
        const data = await response.json();
        // FIX: Assume the API returns the article object directly
        setArticle(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unexpected error occurred.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchArticle();
  }, [id]);

  if (isLoading) {
    return <ActivityIndicator size="large" color={Colors.dark.tint} style={styles.centered} />;
  }

  if (error) {
    return <ThemedText style={styles.centered}>{error}</ThemedText>;
  }

  if (!article) {
    return <ThemedText style={styles.centered}>Article not found.</ThemedText>;
  }

  return (
    <ThemedView style={styles.container}>
      <ScrollView>
        {/* FIX: Construct the full image URL */}
        <ImageBackground source={{ uri: `https://api.waleedzaitoun.com${article.image_path}` }} style={styles.headerImage}>
        </ImageBackground>
        <View style={styles.content}>
          <ThemedText style={styles.title}>{article.title}</ThemedText>
          <ThemedText style={styles.description}>{article.description}</ThemedText>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

// ... (Styles remain the same)
const styles = StyleSheet.create({
  container: { flex: 1 },
  centered: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  headerImage: {
    width: '100%',
    height: 400,
    justifyContent: 'flex-end',
  },
  content: {
    padding: 20,
    marginTop: -30,
    backgroundColor: Colors.dark.background,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  title: {
    fontFamily: 'GE_SS_Two_Bold',
    fontSize: 28,
    color: 'white',
    textAlign: 'right',
    marginBottom: 15,
    lineHeight: 40,
  },
  description: {
    fontFamily: 'GESSTwoLight',
    fontSize: 18,
    color: '#ECEDEE',
    textAlign: 'right',
    lineHeight: 28,
  },
});