// /app/(drawer)/login.tsx
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  Image,
  ImageBackground,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
// 1. Import the useAuth hook to access the signIn function
import { useAuth } from '@/context/AuthContext';
import Toast from 'react-native-toast-message'; // <-- IMPORT TOAST

export default function LoginScreen() {
  const router = useRouter();
  // 2. Get the signIn function from our context
  const { signIn } = useAuth();

  // 3. Add state for inputs and loading status
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [isPasswordVisible, setPasswordVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 4. Create the function to handle the login API call
  const handleLogin = async () => {
    if (!phone || !password) {
      Toast.show({ type: 'error', text1: 'خطأ', text2: 'يرجى إدخال رقم الهاتف وكلمة السر.' });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('https://api.waleedzaitoun.com/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify({
          phone: phone,
          password: password,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        // On success, save the token and navigate
        await signIn(result.token, result.user); // The token is stored in context and AsyncStorage
        Toast.show({ type: 'success', text1: 'نجاح', text2: 'تم تسجيل الدخول بنجاح!' });
        router.replace('/(drawer)/(tabs)/request'); // Navigate to home screen
      } else {
        // On failure, show an error message from the API
        Toast.show({ type: 'error', text1: 'خطأ في الدخول', text2: result.message || 'رقم الهاتف أو كلمة السر غير صحيحة.' });
      }
    } catch (error) {
      console.error('Login API Error:', error);
      Toast.show({ type: 'error', text1: 'خطأ', text2: 'لا يمكن الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ThemedView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <ImageBackground
            source={require('@/assets/backgrounds/hero-background.jpg')}
            style={styles.heroSection}>
            <LinearGradient
              colors={['transparent', 'rgba(16, 20, 30, 0.9)', '#10141E']}
              locations={[0.5, 1, 1]}
              style={styles.heroGradient}
            />
            <View style={styles.heroContent}>
              <ThemedText style={styles.heroTitle}>أهلاً بك من جديد</ThemedText>
              <ThemedText style={styles.heroSubtitle}>
                قم بتسجيل الدخول لحسابك عن طريق رقم الهاتف أو باستخدام جوجل
              </ThemedText>
            </View>
          </ImageBackground>

          <View style={styles.formContainer}>
            {/* Phone Input */}
            <View style={styles.labeledInputContainer}>
              <ThemedText style={styles.label}>رقم الهاتف</ThemedText>
              <View style={styles.inputWrapper}>
                <TextInput
                  placeholder="مثال : 01023456789"
                  placeholderTextColor={Colors.dark.icon}
                  style={styles.input}
                  selectionColor={Colors.dark.tint}
                  keyboardType="phone-pad"
                  value={phone}
                  onChangeText={setPhone} // Set phone state
                />
              </View>
            </View>

            {/* Password Input */}
            <View style={styles.labeledInputContainer}>
              <ThemedText style={styles.label}>كلمة السر</ThemedText>
              <View style={styles.inputWrapper}>
                <TouchableOpacity onPress={() => setPasswordVisible(!isPasswordVisible)}>
                  <Ionicons
                    name={isPasswordVisible ? 'eye-off' : 'eye'}
                    size={24}
                    color={Colors.dark.icon}
                  />
                </TouchableOpacity>
                <TextInput
                  placeholder="كلمة السر"
                  placeholderTextColor={Colors.dark.icon}
                  style={styles.input}
                  selectionColor={Colors.dark.tint}
                  secureTextEntry={!isPasswordVisible}
                  value={password}
                  onChangeText={setPassword} // Set password state
                />
              </View>
            </View>
            
            {/* The "Forgot Password" link is now outside the input component */}
            <TouchableOpacity onPress={() => router.push('/(drawer)/passwordReset')}>
              <ThemedText style={styles.forgotPasswordLink}>هل نسيت كلمة السر؟</ThemedText>
            </TouchableOpacity>

            <TouchableOpacity style={styles.googleButton}>
              <ThemedText style={styles.googleButtonText}>أو الدخول باستخدام جوجل</ThemedText>
              <Image
                source={require('@/assets/icons/google.png')}
                style={styles.googleIcon}
              />
            </TouchableOpacity>

            {/* Login Button */}
            <TouchableOpacity
              style={[styles.button, isLoading && styles.buttonDisabled]}
              onPress={handleLogin} // Call handleLogin function
              disabled={isLoading}>
              <ThemedText style={styles.buttonText}>
                {isLoading ? 'جاري الدخول...' : 'دخول'}
              </ThemedText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}


// Styles (with an added disabled style for the button)
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
  },
  scrollContainer: {
    flexGrow: 1,
  },
  heroSection: {
    height: 700,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  heroGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  heroContent: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 60,
  },
  heroTitle: {
    fontFamily: 'RubikBold',
    fontSize: 28,
    color: '#FFFFFF',
    lineHeight: 40,
    textAlign: 'center',
  },
  heroSubtitle: {
    fontFamily: 'GESSTwo',
    fontSize: 17,
    color: '#FFFFFF',
    textAlign: 'center',
    lineHeight: 28,
    marginTop: 15,
    opacity: 0.9,
  },
  formContainer: {
    padding: 20,
    backgroundColor: Colors.dark.background,
    paddingBottom: 40,
  },
  labeledInputContainer: {
    width: '100%',
    marginBottom: 25,
  },
  label: {
    fontFamily: 'GE_SS_Text_Medium',
    fontSize: 19,
    color: '#007BFF',
    textAlign: 'right',
    marginBottom: 10,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'transparent',
    borderColor: '#007BFF',
    borderWidth: 1,
    borderRadius: 1,
    height: 65,
    paddingHorizontal: 8,
  },
  input: {
    flex: 1,
    color: '#FFFFFF',
    fontFamily: 'GESSTwo',
    fontSize: 16,
    textAlign: 'right',
    paddingHorizontal: 15,
  },
  forgotPasswordLink: {
    fontFamily: 'GESSTwo',
    fontSize: 14,
    color: '#007BFF',
    textAlign: 'right',
    textDecorationLine: 'underline',
    marginTop: -10,
    marginBottom: 30,
  },
  googleButton: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 50,
    marginBottom: 30,
  },
  googleButtonText: {
    fontFamily: 'RubikLight',
    fontSize: 16,
    color: '#FFFFFF',
  },
  button: {
    backgroundColor: '#007BFF',
    paddingVertical: 18,
    width: '100%',
    borderRadius: 15,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#555', // Style for when the button is disabled
  },
  buttonText: {
    fontFamily: 'GE_SS_Two_Bold',
    color: 'white',
    fontSize: 18,
  },
  googleIcon: {
    width: 52,
    height: 52,
  },
});