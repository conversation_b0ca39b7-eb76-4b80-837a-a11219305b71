// /components/NewsSection.tsx
import { Link } from 'expo-router';
import React, { memo, useEffect, useState } from 'react';
import { FlatList, Image, StyleSheet, TouchableOpacity, View } from 'react-native';
import { ThemedText } from './ThemedText';

const NewsCard = ({ item }) => (
  <Link href={`/(drawer)/(tabs)/news/${item.id}`} asChild>
    <TouchableOpacity style={styles.card}>
      {/* FIX 2: Construct the full image URL */}
      <Image source={{ uri: `https://api.waleedzaitoun.com${item.image_path}` }} style={styles.cardImage} />
      <View style={styles.cardContent}>
        <ThemedText style={styles.cardTitle} numberOfLines={2}>{item.title}</ThemedText>
        <ThemedText style={styles.cardDescription} numberOfLines={3}>{item.description}</ThemedText>
      </View>
    </TouchableOpacity>
  </Link>
);

export const NewsSection = memo(() => {
  const [news, setNews] = useState([]);

  useEffect(() => {
    const fetchNews = async () => {
      try {
        const response = await fetch('https://api.waleedzaitoun.com/api/news');
        const data = await response.json();
        // FIX 1: The API returns a direct array
        setNews(data.slice(0, 5)); // Show only the first 5 articles
      } catch (error) {
        console.error("Failed to fetch news for section:", error);
      }
    };
    fetchNews();
  }, []);

  return (
    <View style={styles.container}>
      <Image
        source={require('@/assets/backgrounds/hero-background.jpg')}
        style={styles.backgroundImage}
      />
      <ThemedText style={styles.mainTitle}>أخر الأخبار</ThemedText>
      <FlatList
        horizontal
        data={news}
        renderItem={({ item }) => <NewsCard item={item} />}
        keyExtractor={(item) => item.id.toString()}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.carouselContainer}
        inverted // RTL support
      />
      <Link href="/(drawer)/(tabs)/news" asChild>
        <TouchableOpacity style={styles.button}>
          <ThemedText style={styles.buttonText}>عرض الكل</ThemedText>
        </TouchableOpacity>
      </Link>
    </View>
  );
});

// --- UPDATED STYLES ---
const styles = StyleSheet.create({
    container: {
    paddingVertical: 270,
    position: 'relative',
  },
  backgroundImage: {
    ...StyleSheet.absoluteFillObject,
    width: '100%',
    height: '100%',
  },
  mainTitle: {
    fontFamily: 'GE_SS_Text_Medium',
    fontSize: 32,
    color: 'white',
    textAlign: 'right',
    lineHeight: 40,
    marginRight: 30,
  },
  carouselContainer: {
    paddingHorizontal: 15,
  },
  // Card styles updated to match the image
  card: {
    backgroundColor: '#D9D9D9',
    borderRadius: 20,
    width: 280,
    marginHorizontal: 10,
    padding: 15, // Add padding to the card itself
  },
  // Image styles updated
  cardImage: {
    width: '100%', // Takes the full width of the padded card area
    height: 250,
    borderRadius: 15, // Rounded corners for the image itself
  },
  // Content styles updated
  cardContent: {
    paddingTop: 15, // Space between image and title
  },
  // Title text color updated
  cardTitle: {
    fontFamily: 'GE_SS_Two_Bold',
    fontSize: 17,
    color: '#000000ff',
    textAlign: 'right',
  },
  // Description text color updated
  cardDescription: {
    fontFamily: 'GESSTwoLight',
    fontSize: 16,
    color: '#10141E',
    textAlign: 'right',
    marginTop: 5,
  },
  button: {
    backgroundColor: '#007BFF',
    paddingVertical: 18,
    marginHorizontal: '5%',
    borderRadius: 15,
    alignItems: 'center',
    marginTop: 40,
  },
  buttonText: {
    fontFamily: 'GE_SS_Text_Medium',
    color: 'white',
    fontSize: 24,
  },
});

NewsSection.displayName = "NewsSection";