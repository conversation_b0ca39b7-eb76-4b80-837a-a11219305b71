// /app/(drawer)/(tabs)/register.tsx
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';

import {
  Alert,
  Image,
  ImageBackground,
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

// 1. Import the new auth module and our custom hook
import { useAuth } from '@/context/AuthContext';
import auth from '@react-native-firebase/auth';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';

const kafrElSheikhCities = [
  'كفر الشيخ (العاصمة)', 'دسوق', 'فوه', 'مطوبس', 'بلطيم', 'الحامول', 'بيلا', 'الرياض', 'سيدي سالم', 'قلين', 'برج البرلس',
];

// --- LabeledInput and LabeledPicker components remain unchanged ---
const LabeledInput = ({
  label,
  placeholder,
  value,
  onChangeText,
  keyboardType = 'default',
}: {
  label: string;
  placeholder: string;
  value: string;
  onChangeText: (text: string) => void;
  keyboardType?: 'default' | 'phone-pad' | 'email-address';
}) => (
  <View style={styles.labeledInputContainer}>
    <ThemedText style={styles.label}>{label}</ThemedText>
    <View style={styles.inputWrapper}>
      <TextInput
        placeholder={placeholder}
        placeholderTextColor={Colors.dark.icon}
        style={styles.input}
        keyboardType={keyboardType}
        selectionColor={Colors.dark.tint}
        value={value}
        onChangeText={onChangeText}
      />
    </View>
  </View>
);

const LabeledPicker = ({
  label,
  placeholder,
  items,
  selectedValue,
  onValueChange,
}: {
  label: string;
  placeholder: string;
  items: string[];
  selectedValue: string;
  onValueChange: (value: string) => void;
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const handleSelect = (item: string) => {
    onValueChange(item);
    setModalVisible(false);
  };
  return (
    <>
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}>
        <TouchableOpacity
          style={styles.modalContainer}
          activeOpacity={1}
          onPressOut={() => setModalVisible(false)}>
          <View style={styles.modalContent}>
            <ScrollView>
              {items.map((item) => (
                <TouchableOpacity
                  key={item}
                  style={styles.modalItem}
                  onPress={() => handleSelect(item)}>
                  <Text style={styles.modalItemText}>{item}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>
      <View style={styles.labeledInputContainer}>
        <ThemedText style={styles.label}>{label}</ThemedText>
        <TouchableOpacity
          style={[styles.inputWrapper, styles.pickerWrapper]}
          onPress={() => setModalVisible(true)}>
          <ThemedText style={selectedValue ? styles.pickerTextSelected : styles.pickerText}>
            {selectedValue || placeholder}
          </ThemedText>
          <Ionicons name="chevron-down" size={20} color={Colors.dark.icon} />
        </TouchableOpacity>
      </View>
    </>
  );
};


export default function RegisterScreen() {
  const router = useRouter();
  const { authToken, setConfirmation } = useAuth();
  
  const [name, setName] = useState('');
  const [center, setCenter] = useState('');
  const [address, setAddress] = useState('');
  const [phone, setPhone] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (authToken) {
      router.replace('/(drawer)/(tabs)/request');
    }
  }, [authToken, router]);

  const handleCreateAccount = async () => {
    if (!name || !phone || !address || !center) {
      Toast.show({ type: 'error', text1: 'خطأ', text2: 'يرجى ملء جميع الحقول.' });
      return;
    }
    const phoneRegex = /^01[0-9]{9}$/;
    if (!phoneRegex.test(phone)) {
      Alert.alert('خطأ', 'يرجى إدخال رقم هاتف صحيح (01xxxxxxxxx)');
      return;
    }

    setIsLoading(true);

    try {
      // --- Step 1: Check if the phone number is already registered ---
      const checkResponse = await fetch("https://api.waleedzaitoun.com/api/auth/check-phone", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({ phone }),
      });

      const checkResult = await checkResponse.json();

      // --- Step 2: Check the 'exists' property from the response body ---
      if (checkResult.exists) {
        Alert.alert(
          'رقم الهاتف مسجل',
          'هذا الرقم مسجل بالفعل. هل تريد تسجيل الدخول بدلاً من ذلك؟',
          [
            { text: 'إلغاء', style: 'cancel' },
            { text: 'تسجيل الدخول', onPress: () => router.push('/(drawer)/login') }
          ]
        );
        setIsLoading(false);
        return;
      }
      
      // --- Step 3: If the number does not exist, proceed with sending the OTP ---
      const fullPhoneNumber = `+20${phone.substring(1)}`;
      const confirmationResult = await auth().signInWithPhoneNumber(fullPhoneNumber);

      setConfirmation(confirmationResult);

      router.push({
        pathname: '/(drawer)/otp',
        params: { 
          name,
          center,
          address,
          phone: fullPhoneNumber,
          verificationId: confirmationResult.verificationId,
        },
      });

    } catch (error: any) {
      if (error.code === 'auth/invalid-phone-number') {
        Alert.alert('خطأ', 'رقم الهاتف الذي أدخلته غير صالح.');
      } else if (error.code === 'auth/too-many-requests') {
          Alert.alert('خطأ', 'تم إرسال عدد كبير من الطلبات من هذا الجهاز. يرجى المحاولة مرة أخرى لاحقاً.');
      } else {
        console.error('Registration or Phone Check Error:', error);
        Alert.alert('خطأ', 'حدث خطأ ما. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.');
      }
    } finally {
        setIsLoading(false);
    }
  };

  if (authToken) {
    return null;
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ThemedView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <ImageBackground
            source={require('@/assets/backgrounds/hero-background.jpg')}
            style={styles.heroSection}>
            <LinearGradient
              colors={['transparent', 'rgba(16, 20, 30, 0.9)', '#10141E']}
              locations={[0.3, 0.7, 1]}
              style={styles.heroGradient}
            />
            <View style={styles.heroContent}>
              <ThemedText style={styles.heroTitle}>أهلاً بك</ThemedText>
              <ThemedText style={styles.heroSubtitle}>
                فى الموقع الرسمى لمكتب خدمة الجمهور الإلكترونى الأول فى مصر لرجل الأعمال وليد محمد زيتون
              </ThemedText>
              <ThemedText style={styles.heroDescription}>
                قم بتسجيل بياناتك لسهولة التواصل مع فريق العمل لدينا و سرعة تقديم الطلبات.
              </ThemedText>
              <TouchableOpacity onPress={() => router.push('/(drawer)/login')}>
                <ThemedText style={styles.heroLink}>أو يمكنك تسجيل الدخول من هنا</ThemedText>
              </TouchableOpacity>
            </View>
          </ImageBackground>
          <View style={styles.formContainer}>
            <LabeledInput label="الاسم" placeholder="ادخل اسمك رباعى ...." value={name} onChangeText={setName} />
            <LabeledPicker
              label="المركز"
              placeholder="اختر المركز أو المدينة..."
              items={kafrElSheikhCities}
              selectedValue={center}
              onValueChange={setCenter}
            />
            <LabeledInput label="العنوان" placeholder="ادخل عنوانك بالتفصيل ...." value={address} onChangeText={setAddress} />
            <LabeledInput
              label="رقم الهاتف"
              placeholder="مثال : ***********"
              keyboardType="phone-pad"
              value={phone}
              onChangeText={setPhone}
            />
            <TouchableOpacity style={[styles.button, isLoading && styles.buttonDisabled]} onPress={handleCreateAccount} disabled={isLoading}>
              <ThemedText style={styles.buttonText}>{isLoading ? 'جاري الإرسال...' : 'إنشاء الحساب'}</ThemedText>
            </TouchableOpacity>
             <View style={styles.dividerContainer}>
              <View style={styles.dividerLine} />
              <ThemedText style={styles.dividerText}>أو التسجيل باستخدام</ThemedText>
              <View style={styles.dividerLine} />
            </View>
            <TouchableOpacity style={styles.googleButton}>
              <Image
                source={require('@/assets/icons/google.png')}
                style={styles.googleIcon}
              />
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

// Styles remain unchanged
const styles = StyleSheet.create({
  container: { flex: 1, paddingTop: 0 },
  scrollContainer: { flexGrow: 1, paddingTop: 40 },
  heroSection: { height: 650, justifyContent: 'flex-end', alignItems: 'center' },
  heroGradient: { ...StyleSheet.absoluteFillObject },
  heroContent: { alignItems: 'flex-end', paddingBottom: 0 },
  heroTitle: { fontFamily: 'RubikBold', fontSize: 27, color: '#FFFFFF', lineHeight: 40, paddingRight: 20 },
  heroSubtitle: { fontFamily: 'GESSTwoLight', fontSize: 18, color: '#FFFFFF', textAlign: 'right', lineHeight: 20, paddingRight: 20 },
  heroDescription: { fontFamily: 'GESSTwoLight', fontSize: 15, textAlign: 'right', marginTop: 10, lineHeight: 20, paddingRight: 20 },
  heroLink: { fontFamily: 'GESSTwo', color: '#007BFF', fontSize: 17, textDecorationLine: 'underline', paddingRight: 20 },
  formContainer: { padding: 20, paddingBottom: 40 },
  labeledInputContainer: { width: '100%', marginBottom: 25 },
  label: { fontFamily: 'GE_SS_Text_Medium', fontSize: 19, color: '#007BFF', textAlign: 'right', marginBottom: 10 },
  inputWrapper: { backgroundColor: 'transparent', borderColor: '#007BFF', borderWidth: 1, borderRadius: 1, height: 65, justifyContent: 'center', paddingHorizontal: 15 },
  input: { color: '#FFFFFF', fontFamily: 'GESSTwo', fontSize: 16, textAlign: 'right', width: '100%', height: '100%' },
  pickerWrapper: { flexDirection: 'row-reverse', justifyContent: 'space-between', alignItems: 'center', height: 65 },
  pickerText: { color: Colors.dark.icon, fontFamily: 'GESSTwo', fontSize: 16, textAlign: 'right' },
  pickerTextSelected: { color: '#FFFFFF', fontFamily: 'GESSTwo', fontSize: 16, textAlign: 'right' },
  button: { backgroundColor: '#007BFF', paddingVertical: 18, width: '100%', borderRadius: 15, alignItems: 'center', marginTop: 10 },
  buttonDisabled: {
    backgroundColor: '#555', // Grey color when disabled
  },
  buttonText: { fontFamily: 'GE_SS_Two_Bold', color: 'white', fontSize: 18 },
  dividerContainer: { flexDirection: 'row', alignItems: 'center', justifyContent: 'center', marginTop: 30, marginBottom: 20 },
  dividerLine: { flex: 1, height: 1, backgroundColor: '#007BFF' },
  dividerText: { color: '#FFFFFF', fontFamily: 'RubikLight', fontSize: 18, marginHorizontal: 5 },
  googleButton: { alignSelf: 'center' },
  googleIcon: { width: 52, height: 52 },
  modalContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0, 0, 0, 0.7)' },
  modalContent: { backgroundColor: '#1E232C', borderRadius: 10, padding: 20, width: '80%', maxHeight: '60%' },
  modalItem: { paddingVertical: 15, borderBottomWidth: 1, borderBottomColor: '#333A45' },
  modalItemText: { color: '#FFFFFF', fontSize: 18, textAlign: 'center', fontFamily: 'GESSTwo' },
});