# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec cmake_object_order_depends_target_react_codegen_RNEdgeToEdge cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen cmake_object_order_depends_target_react_codegen_rnasyncstorage cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen cmake_object_order_depends_target_react_codegen_rnreanimated cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_rnsvg cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/D_/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_Debug D$:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\D_\dev\Zayton\android\app\build\generated\autolinking\src\main\jni\autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir\D_\dev\Zayton\android\app\build\generated\autolinking\src\main\jni

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_Debug D$:/dev/Zayton/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library D:\dev\Zayton\android\app\build\intermediates\cxx\Debug\44546z54\obj\armeabi-v7a\libappmodules.so

build D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_Debug rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o CMakeFiles/appmodules.dir/D_/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_safeareacontext.so D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnscreens.so D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnsvg.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so || D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnscreens.so D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnsvg.so D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_safeareacontext.so RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge RNGoogleSignInCGen_autolinked_build/react_codegen_RNGoogleSignInCGen rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen rnreanimated_autolinked_build/react_codegen_rnreanimated
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = D:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_safeareacontext.so  D:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnscreens.so  D:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnsvg.so  "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so"  -latomic -lm
  OBJECT_DIR = CMakeFiles\appmodules.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = D:\dev\Zayton\android\app\build\intermediates\cxx\Debug\44546z54\obj\armeabi-v7a\libappmodules.so
  TARGET_PDB = appmodules.so.dbg
  RSP_FILE = CMakeFiles\appmodules.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a && C:\SDK\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a && C:\SDK\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\dev\Zayton\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnasyncstorage


#############################################
# Order-only phony target for react_codegen_rnasyncstorage

build cmake_object_order_depends_target_react_codegen_rnasyncstorage: phony || rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\rnasyncstorageJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\rnasyncstorage-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir



#############################################
# Object library react_codegen_rnasyncstorage

build rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\rnasyncstorage_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnasyncstorage_autolinked_build/edit_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\rnasyncstorage_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\dev\Zayton\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/rebuild_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNGoogleSignInCGen


#############################################
# Order-only phony target for react_codegen_RNGoogleSignInCGen

build cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen: phony || RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_Debug D$:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/RNGoogleSignInCGen-generated.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\RNGoogleSignInCGen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_Debug D$:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_Debug D$:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_Debug D$:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/Props.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_Debug D$:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\RNGoogleSignInCGenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_Debug D$:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_Debug D$:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/States.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen



#############################################
# Object library react_codegen_RNGoogleSignInCGen

build RNGoogleSignInCGen_autolinked_build/react_codegen_RNGoogleSignInCGen: phony RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o


#############################################
# Utility command for edit_cache

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\RNGoogleSignInCGen_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNGoogleSignInCGen_autolinked_build/edit_cache: phony RNGoogleSignInCGen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\RNGoogleSignInCGen_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\dev\Zayton\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNGoogleSignInCGen_autolinked_build/rebuild_cache: phony RNGoogleSignInCGen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rngesturehandler_codegen


#############################################
# Order-only phony target for react_codegen_rngesturehandler_codegen

build cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen: phony || rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\rngesturehandler_codegenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\rngesturehandler_codegen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir



#############################################
# Object library react_codegen_rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o


#############################################
# Utility command for edit_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\rngesturehandler_codegen_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rngesturehandler_codegen_autolinked_build/edit_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\rngesturehandler_codegen_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\dev\Zayton\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rngesturehandler_codegen_autolinked_build/rebuild_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnreanimated


#############################################
# Order-only phony target for react_codegen_rnreanimated

build cmake_object_order_depends_target_react_codegen_rnreanimated: phony || rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\rnreanimatedJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\rnreanimated-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir



#############################################
# Object library react_codegen_rnreanimated

build rnreanimated_autolinked_build/react_codegen_rnreanimated: phony rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnreanimated_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\rnreanimated_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnreanimated_autolinked_build/edit_cache: phony rnreanimated_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\rnreanimated_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\dev\Zayton\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnreanimated_autolinked_build/rebuild_cache: phony rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8a26c049427bcc82f1d830398b2cfb87/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/dev/Zayton/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8a26c049427bcc82f1d830398b2cfb87\cpp\react\renderer\components\safeareacontext\RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8a26c049427bcc82f1d830398b2cfb87\cpp\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/917f708e242e4d8be12911227a568149/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/dev/Zayton/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\917f708e242e4d8be12911227a568149\common\cpp\react\renderer\components\safeareacontext\RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\917f708e242e4d8be12911227a568149\common\cpp\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4060490531b5fb00f72034607561b9fc/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/dev/Zayton/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\4060490531b5fb00f72034607561b9fc\jni\react\renderer\components\safeareacontext\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\4060490531b5fb00f72034607561b9fc\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/037303a3626942774a233183d6d53c2b/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/dev/Zayton/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\037303a3626942774a233183d6d53c2b\codegen\jni\react\renderer\components\safeareacontext\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\037303a3626942774a233183d6d53c2b\codegen\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3ab5fe48a452a57eb5718883907c7eba/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/dev/Zayton/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3ab5fe48a452a57eb5718883907c7eba\source\codegen\jni\react\renderer\components\safeareacontext\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3ab5fe48a452a57eb5718883907c7eba\source\codegen\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3ab5fe48a452a57eb5718883907c7eba/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/dev/Zayton/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3ab5fe48a452a57eb5718883907c7eba\source\codegen\jni\react\renderer\components\safeareacontext\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3ab5fe48a452a57eb5718883907c7eba\source\codegen\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3ab5fe48a452a57eb5718883907c7eba/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/dev/Zayton/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3ab5fe48a452a57eb5718883907c7eba\source\codegen\jni\react\renderer\components\safeareacontext\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3ab5fe48a452a57eb5718883907c7eba\source\codegen\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3953892f01f03e92fc63e80e328f0d4a/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/dev/Zayton/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3953892f01f03e92fc63e80e328f0d4a\react\renderer\components\safeareacontext\safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3953892f01f03e92fc63e80e328f0d4a\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/917f708e242e4d8be12911227a568149/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/dev/Zayton/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\917f708e242e4d8be12911227a568149\android\build\generated\source\codegen\jni\safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\917f708e242e4d8be12911227a568149\android\build\generated\source\codegen\jni


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library D:\dev\Zayton\android\app\build\intermediates\cxx\Debug\44546z54\obj\armeabi-v7a\libreact_codegen_safeareacontext.so

build D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_Debug safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8a26c049427bcc82f1d830398b2cfb87/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/917f708e242e4d8be12911227a568149/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4060490531b5fb00f72034607561b9fc/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/037303a3626942774a233183d6d53c2b/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3ab5fe48a452a57eb5718883907c7eba/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3ab5fe48a452a57eb5718883907c7eba/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3ab5fe48a452a57eb5718883907c7eba/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3953892f01f03e92fc63e80e328f0d4a/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/917f708e242e4d8be12911227a568149/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so"  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = D:\dev\Zayton\android\app\build\intermediates\cxx\Debug\44546z54\obj\armeabi-v7a\libreact_codegen_safeareacontext.so
  TARGET_PDB = react_codegen_safeareacontext.so.dbg


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\safeareacontext_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\safeareacontext_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\dev\Zayton\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\common\cpp\react\renderer\components\rnscreens\RNSFullWindowOverlayShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\common\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\common\cpp\react\renderer\components\rnscreens\RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\common\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/D_/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\D_\dev\Zayton\node_modules\react-native-screens\common\cpp\react\renderer\components\rnscreens\RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\D_\dev\Zayton\node_modules\react-native-screens\common\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\common\cpp\react\renderer\components\rnscreens\RNSScreenStackHeaderConfigShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\common\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\common\cpp\react\renderer\components\rnscreens\RNSScreenStackHeaderConfigState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\common\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\common\cpp\react\renderer\components\rnscreens\RNSScreenStackHeaderSubviewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\common\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\common\cpp\react\renderer\components\rnscreens\RNSScreenStackHeaderSubviewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\common\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/D_/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\D_\dev\Zayton\node_modules\react-native-screens\common\cpp\react\renderer\components\rnscreens\RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\D_\dev\Zayton\node_modules\react-native-screens\common\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/910b561b42ee9ec61144ee4f3e7c7162/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\910b561b42ee9ec61144ee4f3e7c7162\generated\source\codegen\jni\react\renderer\components\rnscreens\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\910b561b42ee9ec61144ee4f3e7c7162\generated\source\codegen\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/26fefe7d2ba27d8025af95104418f116/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\26fefe7d2ba27d8025af95104418f116\build\generated\source\codegen\jni\react\renderer\components\rnscreens\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\26fefe7d2ba27d8025af95104418f116\build\generated\source\codegen\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\android\build\generated\source\codegen\jni\react\renderer\components\rnscreens\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\android\build\generated\source\codegen\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/26fefe7d2ba27d8025af95104418f116/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\26fefe7d2ba27d8025af95104418f116\build\generated\source\codegen\jni\react\renderer\components\rnscreens\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\26fefe7d2ba27d8025af95104418f116\build\generated\source\codegen\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\android\build\generated\source\codegen\jni\react\renderer\components\rnscreens\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6e0e1aab7bcc62b53b2923a817264dd7\android\build\generated\source\codegen\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9bf7d20490a9d83c10546c1158a394f6/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/dev/Zayton/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9bf7d20490a9d83c10546c1158a394f6\source\codegen\jni\react\renderer\components\rnscreens\rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9bf7d20490a9d83c10546c1158a394f6\source\codegen\jni\react\renderer\components\rnscreens


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library D:\dev\Zayton\android\app\build\intermediates\cxx\Debug\44546z54\obj\armeabi-v7a\libreact_codegen_rnscreens.so

build D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_Debug rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/D_/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/D_/dev/Zayton/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/910b561b42ee9ec61144ee4f3e7c7162/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/26fefe7d2ba27d8025af95104418f116/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/26fefe7d2ba27d8025af95104418f116/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6e0e1aab7bcc62b53b2923a817264dd7/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9bf7d20490a9d83c10546c1158a394f6/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so"  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = D:\dev\Zayton\android\app\build\intermediates\cxx\Debug\44546z54\obj\armeabi-v7a\libreact_codegen_rnscreens.so
  TARGET_PDB = react_codegen_rnscreens.so.dbg


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\rnscreens_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\rnscreens_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\dev\Zayton\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Order-only phony target for react_codegen_rnsvg

build cmake_object_order_depends_target_react_codegen_rnsvg: phony || rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\D_\dev\Zayton\node_modules\react-native-svg\common\cpp\react\renderer\components\rnsvg\RNSVGImageShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\D_\dev\Zayton\node_modules\react-native-svg\common\cpp\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\D_\dev\Zayton\node_modules\react-native-svg\common\cpp\react\renderer\components\rnsvg\RNSVGImageState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\D_\dev\Zayton\node_modules\react-native-svg\common\cpp\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\D_\dev\Zayton\node_modules\react-native-svg\common\cpp\react\renderer\components\rnsvg\RNSVGLayoutableShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\D_\dev\Zayton\node_modules\react-native-svg\common\cpp\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\D_\dev\Zayton\node_modules\react-native-svg\common\cpp\react\renderer\components\rnsvg\RNSVGShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\D_\dev\Zayton\node_modules\react-native-svg\common\cpp\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/rnsvg.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\rnsvg.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/6241aae3cc620ac17b0d3702c983eced/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/dev/Zayton/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\6241aae3cc620ac17b0d3702c983eced\build\generated\source\codegen\jni\react\renderer\components\rnsvg\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\6241aae3cc620ac17b0d3702c983eced\build\generated\source\codegen\jni\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/c01f95d0ffa395a187816a33968304b9/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/dev/Zayton/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\c01f95d0ffa395a187816a33968304b9\android\build\generated\source\codegen\jni\react\renderer\components\rnsvg\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\c01f95d0ffa395a187816a33968304b9\android\build\generated\source\codegen\jni\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/dev/Zayton/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\D_\dev\Zayton\node_modules\react-native-svg\android\build\generated\source\codegen\jni\react\renderer\components\rnsvg\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\D_\dev\Zayton\node_modules\react-native-svg\android\build\generated\source\codegen\jni\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/c01f95d0ffa395a187816a33968304b9/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/dev/Zayton/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\c01f95d0ffa395a187816a33968304b9\android\build\generated\source\codegen\jni\react\renderer\components\rnsvg\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\c01f95d0ffa395a187816a33968304b9\android\build\generated\source\codegen\jni\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/dev/Zayton/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\D_\dev\Zayton\node_modules\react-native-svg\android\build\generated\source\codegen\jni\react\renderer\components\rnsvg\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\D_\dev\Zayton\node_modules\react-native-svg\android\build\generated\source\codegen\jni\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/c01f95d0ffa395a187816a33968304b9/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/dev/Zayton/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\c01f95d0ffa395a187816a33968304b9\android\build\generated\source\codegen\jni\react\renderer\components\rnsvg\rnsvgJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/. -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\c01f95d0ffa395a187816a33968304b9\android\build\generated\source\codegen\jni\react\renderer\components\rnsvg


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Link the shared library D:\dev\Zayton\android\app\build\intermediates\cxx\Debug\44546z54\obj\armeabi-v7a\libreact_codegen_rnsvg.so

build D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnsvg.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnsvg_Debug rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/6241aae3cc620ac17b0d3702c983eced/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/c01f95d0ffa395a187816a33968304b9/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/c01f95d0ffa395a187816a33968304b9/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/D_/dev/Zayton/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/c01f95d0ffa395a187816a33968304b9/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so"  -latomic -lm
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnsvg.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = D:\dev\Zayton\android\app\build\intermediates\cxx\Debug\44546z54\obj\armeabi-v7a\libreact_codegen_rnsvg.so
  TARGET_PDB = react_codegen_rnsvg.so.dbg


#############################################
# Utility command for edit_cache

build rnsvg_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\rnsvg_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnsvg_autolinked_build/edit_cache: phony rnsvg_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\rnsvg_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\dev\Zayton\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnsvg_autolinked_build/rebuild_cache: phony rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNCWebViewSpec


#############################################
# Order-only phony target for react_codegen_RNCWebViewSpec

build cmake_object_order_depends_target_react_codegen_RNCWebViewSpec: phony || RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug D$:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\RNCWebViewSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug D$:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug D$:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug D$:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug D$:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\RNCWebViewSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug D$:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug D$:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec



#############################################
# Object library react_codegen_RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec: phony RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNCWebViewSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\RNCWebViewSpec_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNCWebViewSpec_autolinked_build/edit_cache: phony RNCWebViewSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNCWebViewSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\RNCWebViewSpec_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\dev\Zayton\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNCWebViewSpec_autolinked_build/rebuild_cache: phony RNCWebViewSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNEdgeToEdge


#############################################
# Order-only phony target for react_codegen_RNEdgeToEdge

build cmake_object_order_depends_target_react_codegen_RNEdgeToEdge: phony || RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug D$:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/RNEdgeToEdge-generated.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\RNEdgeToEdge-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug D$:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug D$:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug D$:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/Props.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug D$:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge\RNEdgeToEdgeJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug D$:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug D$:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/States.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/9970e86d3d8ec320aeeff3c5deadaad5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/b24c8639f7c924b1c08482d0e5be29b1/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge



#############################################
# Object library react_codegen_RNEdgeToEdge

build RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge: phony RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o


#############################################
# Utility command for edit_cache

build RNEdgeToEdge_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\RNEdgeToEdge_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNEdgeToEdge_autolinked_build/edit_cache: phony RNEdgeToEdge_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNEdgeToEdge_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a\RNEdgeToEdge_autolinked_build && C:\SDK\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\dev\Zayton\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\dev\Zayton\android\app\.cxx\Debug\44546z54\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNEdgeToEdge_autolinked_build/rebuild_cache: phony RNEdgeToEdge_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libappmodules.so

build libappmodules.so: phony D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libappmodules.so

build libreact_codegen_rnscreens.so: phony D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnscreens.so

build libreact_codegen_rnsvg.so: phony D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnsvg.so

build libreact_codegen_safeareacontext.so: phony D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

build react_codegen_RNCWebViewSpec: phony RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec

build react_codegen_RNEdgeToEdge: phony RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge

build react_codegen_RNGoogleSignInCGen: phony RNGoogleSignInCGen_autolinked_build/react_codegen_RNGoogleSignInCGen

build react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

build react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

build react_codegen_rnreanimated: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

build react_codegen_rnscreens: phony D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnscreens.so

build react_codegen_rnsvg: phony D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnsvg.so

build react_codegen_safeareacontext: phony D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a

build all: phony D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libappmodules.so rnasyncstorage_autolinked_build/all RNGoogleSignInCGen_autolinked_build/all rngesturehandler_codegen_autolinked_build/all rnreanimated_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all rnsvg_autolinked_build/all RNCWebViewSpec_autolinked_build/all RNEdgeToEdge_autolinked_build/all

# =============================================================================

#############################################
# Folder: D:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/RNCWebViewSpec_autolinked_build

build RNCWebViewSpec_autolinked_build/all: phony RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec

# =============================================================================

#############################################
# Folder: D:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/RNEdgeToEdge_autolinked_build

build RNEdgeToEdge_autolinked_build/all: phony RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge

# =============================================================================

#############################################
# Folder: D:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/RNGoogleSignInCGen_autolinked_build

build RNGoogleSignInCGen_autolinked_build/all: phony RNGoogleSignInCGen_autolinked_build/react_codegen_RNGoogleSignInCGen

# =============================================================================

#############################################
# Folder: D:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/rnasyncstorage_autolinked_build

build rnasyncstorage_autolinked_build/all: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

# =============================================================================

#############################################
# Folder: D:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/rngesturehandler_codegen_autolinked_build

build rngesturehandler_codegen_autolinked_build/all: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

# =============================================================================

#############################################
# Folder: D:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/rnreanimated_autolinked_build

build rnreanimated_autolinked_build/all: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

# =============================================================================

#############################################
# Folder: D:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: D:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/rnsvg_autolinked_build

build rnsvg_autolinked_build/all: phony D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_rnsvg.so

# =============================================================================

#############################################
# Folder: D:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony D$:/dev/Zayton/android/app/build/intermediates/cxx/Debug/44546z54/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/CMakeFiles/cmake.verify_globs | C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/abis.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/flags.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake D$:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake D$:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake D$:/dev/Zayton/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/abis.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/flags.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/SDK/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake D$:/dev/Zayton/android/app/.cxx/Debug/44546z54/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake D$:/dev/Zayton/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake D$:/dev/Zayton/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake D$:/dev/Zayton/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt D$:/dev/Zayton/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
