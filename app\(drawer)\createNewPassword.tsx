// /app/(drawer)/createNewPassword.tsx
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  ImageBackground,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';

const LabeledPasswordInput = ({
  label,
  placeholder,
  value,
  onChangeText,
}: {
  label: string;
  placeholder: string;
  value: string;
  onChangeText: (text: string) => void;
}) => {
  const [isPasswordVisible, setPasswordVisible] = useState(false);
  return (
    <View style={styles.labeledInputContainer}>
      <ThemedText style={styles.label}>{label}</ThemedText>
      <View style={styles.inputWrapper}>
        <TouchableOpacity onPress={() => setPasswordVisible(!isPasswordVisible)}>
          <Ionicons name={isPasswordVisible ? 'eye-off' : 'eye'} size={24} color={Colors.dark.icon} />
        </TouchableOpacity>
        <TextInput
          placeholder={placeholder}
          placeholderTextColor={Colors.dark.icon}
          style={styles.input}
          selectionColor={Colors.dark.tint}
          secureTextEntry={!isPasswordVisible}
          value={value}
          onChangeText={onChangeText}
        />
      </View>
    </View>
  );
};

export default function CreateNewPasswordScreen() {
  const router = useRouter();
  const { phone } = useLocalSearchParams<{ phone: string }>();

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleResetPassword = async () => {
    if (!password || !confirmPassword) {
      Alert.alert('خطأ', 'يرجى إدخال كلمة السر وتأكيدها.');
      return;
    }
    if (password !== confirmPassword) {
      Alert.alert('خطأ', 'كلمتا السر غير متطابقتين.');
      return;
    }
    if (password.length < 8) {
      Alert.alert('خطأ', 'يجب أن تكون كلمة السر 8 أحرف على الأقل.');
      return;
    }
    
    setIsLoading(true);

    try {
      const response = await fetch('https://api.waleedzaitoun.com/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          phone: phone,
          newPassword: password,
          code: '000000', // Backend expects a code, but verification happened on client.
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        Alert.alert(
          'نجاح',
          'تم إعادة تعيين كلمة السر بنجاح. يمكنك الآن تسجيل الدخول.',
          [{ text: 'موافق', onPress: () => router.replace('/(drawer)/login') }]
        );
      } else {
        Alert.alert('خطأ', result.message || 'فشل تحديث كلمة السر.');
      }
    } catch (error) {
      console.error('API Error:', error);
      Alert.alert('خطأ', 'لا يمكن الاتصال بالخادم.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ThemedView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <ImageBackground
            source={require('@/assets/backgrounds/hero-background.jpg')}
            style={styles.heroSection}>
            <LinearGradient
              colors={['transparent', 'rgba(16, 20, 30, 0.9)', '#10141E']}
              locations={[0.5, 1, 1]}
              style={styles.heroGradient}
            />
            <View style={styles.heroContent}>
              <ThemedText style={styles.heroTitle}>إنشاء كلمة سر جديدة</ThemedText>
              <ThemedText style={styles.heroSubtitle}>
                أدخل كلمة سر قوية وجديدة لحسابك.
              </ThemedText>
            </View>
          </ImageBackground>
          <View style={styles.formContainer}>
            <LabeledPasswordInput label="كلمة السر الجديدة" placeholder="اكتب كلمة السر هنا..." value={password} onChangeText={setPassword} />
            <LabeledPasswordInput label="تأكيد كلمة السر الجديدة" placeholder="أعد كتابة كلمة السر هنا..." value={confirmPassword} onChangeText={setConfirmPassword} />
            <TouchableOpacity style={[styles.button, isLoading && styles.buttonDisabled]} onPress={handleResetPassword} disabled={isLoading}>
              <ThemedText style={styles.buttonText}>{isLoading ? 'جاري الحفظ...' : 'حفظ كلمة السر'}</ThemedText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

// Styles are similar to registrationPassword.tsx
const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: Colors.dark.background },
  scrollContainer: { flexGrow: 1 },
  heroSection: { height: 700, justifyContent: 'flex-end', alignItems: 'center' },
  heroGradient: { ...StyleSheet.absoluteFillObject },
  heroContent: { paddingHorizontal: 20, paddingBottom: 60, alignItems: 'center' },
  heroTitle: { fontFamily: 'RubikBold', fontSize: 24, color: '#FFFFFF', textAlign: 'center' },
  heroSubtitle: { fontFamily: 'GESSTwoLight', fontSize: 17, color: '#FFFFFF', textAlign: 'center', lineHeight: 28, marginTop: 12 },
  formContainer: { padding: 20, backgroundColor: Colors.dark.background, paddingBottom: 40 },
  labeledInputContainer: { width: '100%', marginBottom: 25 },
  label: { fontFamily: 'GE_SS_Text_Medium', fontSize: 20, color: '#007BFF', textAlign: 'right', marginBottom: 10 },
  inputWrapper: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', backgroundColor: 'transparent', borderColor: '#007BFF', borderWidth: 1, borderRadius: 1, height: 65, paddingHorizontal: 8 },
  input: { flex: 1, color: '#FFFFFF', fontFamily: 'GESSTwo', fontSize: 16, textAlign: 'right', paddingLeft: 15 },
  button: { backgroundColor: '#007BFF', paddingVertical: 18, width: '100%', borderRadius: 15, alignItems: 'center', marginTop: 20 },
  buttonDisabled: { backgroundColor: '#555' },
  buttonText: { fontFamily: 'GE_SS_Two_Bold', color: 'white', fontSize: 18 },
});