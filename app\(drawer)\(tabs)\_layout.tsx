// /app/(drawer)/(tabs)/_layout.tsx
import { Tabs } from 'expo-router';
import React from 'react';
import { Platform } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const { authToken } = useAuth();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: { position: 'absolute' },
          default: {},
        }),
        tabBarLabelStyle: {
          fontFamily: 'GESSTwo',
          fontSize: 12,
          textTransform: 'none',
          marginBottom: 10,
          lineHeight: 16,
          writingDirection: 'rtl',
          textAlign: 'right',
          marginRight: 5,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'الرئيسية',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="house.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="news"
        options={{
          title: 'الاخبار',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="newspaper" color={color} />,
        }}
      />

      {/* --- THE FIX IS HERE --- */}
      {/*
        We configure BOTH screens, but use `href: null` to dynamically hide one.
        This prevents Expo Router from showing a tab for a file that exists but
        shouldn't be visible in the tab bar.
      */}

      {/* REGISTER SCREEN: Only shown when logged OUT */}
      <Tabs.Screen
        name="register"
        options={{
          title: 'التسجيل',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="person" color={color} />,
          // If user is logged IN (authToken exists), hide this tab.
          href: authToken ? null : '/(drawer)/(tabs)/register',
        }}
      />
      
      {/* REQUEST SCREEN: Only shown when logged IN */}
      <Tabs.Screen
        name="request"
        options={{
          title: 'تقديم طلب',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="document-text" color={color} />,
          // If user is logged OUT (authToken is null), hide this tab.
          href: authToken ? '/(drawer)/(tabs)/request' : null,
        }}
      />
    </Tabs>
  );
}