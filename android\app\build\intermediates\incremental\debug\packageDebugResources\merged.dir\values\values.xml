<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="colorPrimary">#023c69</color>
    <color name="colorPrimaryDark">#ffffff</color>
    <color name="iconBackground">#ffffff</color>
    <color name="splashscreen_background">#ffffff</color>
    <integer name="react_native_dev_server_port">8081</integer>
    <string name="app_name"><PERSON><PERSON><PERSON></string>
    <string name="default_web_client_id" translatable="false">727125081273-71umkb9cbn9nrrkff06kdifqjqsjqdm7.apps.googleusercontent.com</string>
    <string name="expo_splash_screen_resize_mode" translatable="false">contain</string>
    <string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string>
    <string name="expo_system_ui_user_interface_style" translatable="false">automatic</string>
    <string name="gcm_defaultSenderId" translatable="false">727125081273</string>
    <string name="google_api_key" translatable="false">AIzaSyBvlnfnfVnYutgOmsmr6TIjkiFBZkuU9b4</string>
    <string name="google_app_id" translatable="false">1:727125081273:android:edfc198acdb08dab9a6f68</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyBvlnfnfVnYutgOmsmr6TIjkiFBZkuU9b4</string>
    <string name="google_storage_bucket" translatable="false">walidz-4f6cd.firebasestorage.app</string>
    <string name="project_id" translatable="false">walidz-4f6cd</string>
    <style name="AppTheme" parent="Theme.EdgeToEdge">
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#ffffff</item>
  </style>
    <style name="Theme.App.SplashScreen" parent="Theme.SplashScreen">
    <item name="windowSplashScreenBackground">@color/splashscreen_background</item>
    <item name="windowSplashScreenAnimatedIcon">@drawable/splashscreen_logo</item>
    <item name="postSplashScreenTheme">@style/AppTheme</item>
  </style>
</resources>