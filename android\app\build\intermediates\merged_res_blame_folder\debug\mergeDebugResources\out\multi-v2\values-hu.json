{"logs": [{"outputFile": "com.omaraglan96.zayton.app-mergeDebugResources-65:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\23ce214f64706bbaa1149d5e3a6c3d23\\transformed\\play-services-base-18.5.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,480,614,719,883,1017,1135,1241,1407,1511,1692,1825,1993,2161,2228,2292", "endColumns": "106,179,133,104,163,133,117,105,165,103,180,132,167,167,66,63,83", "endOffsets": "299,479,613,718,882,1016,1134,1240,1406,1510,1691,1824,1992,2160,2227,2291,2375"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4886,4997,5181,5319,5428,5596,5734,5856,6143,6313,6421,6606,6743,6915,7087,7158,7226", "endColumns": "110,183,137,108,167,137,121,109,169,107,184,136,171,171,70,67,87", "endOffsets": "4992,5176,5314,5423,5591,5729,5851,5961,6308,6416,6601,6738,6910,7082,7153,7221,7309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7740120f109f93963bab9b03c123192f\\transformed\\play-services-basement-18.4.0\\res\\values-hu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "172", "endOffsets": "367"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5966", "endColumns": "176", "endOffsets": "6138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7979c5b569631585b896f06112c70bce\\transformed\\browser-1.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,252,367", "endColumns": "95,100,114,103", "endOffsets": "146,247,362,466"}, "to": {"startLines": "70,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "7314,7636,7737,7852", "endColumns": "95,100,114,103", "endOffsets": "7405,7732,7847,7951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aeb9e9dd35f43750000a4cf9482a6b3a\\transformed\\appcompat-1.7.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,421,513,628,712,827,950,1027,1102,1193,1286,1381,1475,1575,1668,1763,1858,1949,2040,2123,2233,2343,2443,2554,2663,2782,2964,13672", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "416,508,623,707,822,945,1022,1097,1188,1281,1376,1470,1570,1663,1758,1853,1944,2035,2118,2228,2338,2438,2549,2658,2777,2959,3062,13751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a8af07a92579850ca0fa9ee28fde6d76\\transformed\\core-1.13.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "41,42,43,44,45,46,47,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3780,3877,3979,4081,4182,4285,4392,14621", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3872,3974,4076,4177,4280,4387,4497,14717"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b24c8639f7c924b1c08482d0e5be29b1\\transformed\\react-android-0.79.5-debug\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,215,286,353,432,505,572,645,720,803,892,963,1041,1120,1198,1283,1364,1440,1510,1579,1671,1746,1828,1899", "endColumns": "77,81,70,66,78,72,66,72,74,82,88,70,77,78,77,84,80,75,69,68,91,74,81,70,74", "endOffsets": "128,210,281,348,427,500,567,640,715,798,887,958,1036,1115,1193,1278,1359,1435,1505,1574,1666,1741,1823,1894,1969"}, "to": {"startLines": "33,51,77,79,80,82,96,97,98,145,146,147,148,153,154,155,156,157,158,159,160,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3067,4804,7956,8102,8169,8307,9343,9410,9483,13351,13434,13523,13594,13991,14070,14148,14233,14314,14390,14460,14529,14722,14797,14879,14950", "endColumns": "77,81,70,66,78,72,66,72,74,82,88,70,77,78,77,84,80,75,69,68,91,74,81,70,74", "endOffsets": "3140,4881,8022,8164,8243,8375,9405,9478,9553,13429,13518,13589,13667,14065,14143,14228,14309,14385,14455,14524,14616,14792,14874,14945,15020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2b71d042e5591977e658555b7410cc83\\transformed\\credentials-1.2.0-rc01\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,120", "endOffsets": "160,281"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3145,3255", "endColumns": "109,120", "endOffsets": "3250,3371"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\29acdc0321d00a6f785c8c7eac64d289\\transformed\\material-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1032,1096,1195,1270,1329,1439,1501,1570,1628,1700,1761,1816,1919,1976,2036,2091,2172,2292,2375,2453,2549,2635,2723,2858,2941,3021,3161,3255,3337,3390,3441,3507,3583,3665,3736,3820,3897,3972,4051,4128,4233,4329,4406,4498,4595,4669,4754,4851,4903,4986,5053,5141,5228,5290,5354,5417,5483,5581,5687,5781,5888,5945,6000,6085,6170,6247", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "258,339,415,492,582,662,761,881,964,1027,1091,1190,1265,1324,1434,1496,1565,1623,1695,1756,1811,1914,1971,2031,2086,2167,2287,2370,2448,2544,2630,2718,2853,2936,3016,3156,3250,3332,3385,3436,3502,3578,3660,3731,3815,3892,3967,4046,4123,4228,4324,4401,4493,4590,4664,4749,4846,4898,4981,5048,5136,5223,5285,5349,5412,5478,5576,5682,5776,5883,5940,5995,6080,6165,6242,6315"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,71,72,73,78,81,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150,151,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3376,3457,3533,3610,3700,4502,4601,4721,7410,7473,7537,8027,8248,8380,8490,8552,8621,8679,8751,8812,8867,8970,9027,9087,9142,9223,9558,9641,9719,9815,9901,9989,10124,10207,10287,10427,10521,10603,10656,10707,10773,10849,10931,11002,11086,11163,11238,11317,11394,11499,11595,11672,11764,11861,11935,12020,12117,12169,12252,12319,12407,12494,12556,12620,12683,12749,12847,12953,13047,13154,13211,13266,13756,13841,13918", "endLines": "5,36,37,38,39,40,48,49,50,71,72,73,78,81,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150,151,152", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "308,3452,3528,3605,3695,3775,4596,4716,4799,7468,7532,7631,8097,8302,8485,8547,8616,8674,8746,8807,8862,8965,9022,9082,9137,9218,9338,9636,9714,9810,9896,9984,10119,10202,10282,10422,10516,10598,10651,10702,10768,10844,10926,10997,11081,11158,11233,11312,11389,11494,11590,11667,11759,11856,11930,12015,12112,12164,12247,12314,12402,12489,12551,12615,12678,12744,12842,12948,13042,13149,13206,13261,13346,13836,13913,13986"}}]}]}