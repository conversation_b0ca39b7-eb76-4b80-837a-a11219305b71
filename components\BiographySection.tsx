import React from 'react';
import { StyleSheet, View } from 'react-native';

import { ThemedText } from './ThemedText';

export function BiographySection() {
    return (
        <View style={styles.container}>
            <ThemedText style={styles.biographyText}>وليد محمد فتح الله زيتون مواليد مدينة بلطيم بمركز البرلس بمحافظة كفر الشيخ ، نشأ فى احدى عائلات بلطيم الكبيرة والمتأصلة بالمحافظة عائلة زيتون ، والتى زرعت فيه منذ الصغر حب الوطن والعمل المجتمعى و الاعتماد على الذات ، بدأ مسيرته السياسية مبكراً حيث شارك فى العديد من المبادرات الوطنية والاجتماعية والاستحقاقات الانتخابية بمختلف المهام التوعوية والخدمية و مشاركة الشباب فى الانشطة الرياضية و الاجتماعية ، و على المستوى العملى حقق الناجاحات العملية فى مشروعاته و شارك رؤيته و خبراته فى بناء مجموعة شركات ناجحة فى مجالات مختلفة مما ساعده فى اتاحة فرص عمل للشباب و تمكين العقول الشابة الطموحة فى الإدارات و القطاعات المختلفة حتى اجتمعت أصالة العائلة و شعبيته الاجتماعية و العملية فى تأييده و ترشيحه لكتابة فصل جديد من النجاح فى العمل السياسى و الاجتماعى و خدمة الوطن.
            </ThemedText>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#007BFF', // The bright blue background from the image
        padding: 20,
        marginTop: -1, // Helps resolve any potential pixel-thin gap between sections
        alignItems: 'flex-end',
        
    },
    biographyText: {
        fontFamily: 'GESSTwoLight', // Using the custom font
        fontSize: 19,
        color: 'white',
        lineHeight: 30,
        textAlign: 'justify',
        writingDirection: 'rtl',
    },
});