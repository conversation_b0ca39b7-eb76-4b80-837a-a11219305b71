{"logs": [{"outputFile": "com.omaraglan96.zayton.app-mergeDebugResources-65:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7740120f109f93963bab9b03c123192f\\transformed\\play-services-basement-18.4.0\\res\\values-or\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5753", "endColumns": "139", "endOffsets": "5888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\23ce214f64706bbaa1149d5e3a6c3d23\\transformed\\play-services-base-18.5.0\\res\\values-or\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,457,585,698,849,980,1090,1196,1359,1468,1625,1754,1900,2053,2114,2182", "endColumns": "106,156,127,112,150,130,109,105,162,108,156,128,145,152,60,67,82", "endOffsets": "299,456,584,697,848,979,1089,1195,1358,1467,1624,1753,1899,2052,2113,2181,2264"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4718,4829,4990,5122,5239,5394,5529,5643,5893,6060,6173,6334,6467,6617,6774,6839,6911", "endColumns": "110,160,131,116,154,134,113,109,166,112,160,132,149,156,64,71,86", "endOffsets": "4824,4985,5117,5234,5389,5524,5638,5748,6055,6168,6329,6462,6612,6769,6834,6906,6993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2b71d042e5591977e658555b7410cc83\\transformed\\credentials-1.2.0-rc01\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,111", "endOffsets": "162,274"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3074,3186", "endColumns": "111,111", "endOffsets": "3181,3293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aeb9e9dd35f43750000a4cf9482a6b3a\\transformed\\appcompat-1.7.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,326,433,519,623,743,822,903,994,1087,1188,1283,1383,1476,1571,1667,1758,1848,1937,2047,2151,2257,2368,2470,2588,2751,2857", "endColumns": "110,109,106,85,103,119,78,80,90,92,100,94,99,92,94,95,90,89,88,109,103,105,110,101,117,162,105,89", "endOffsets": "211,321,428,514,618,738,817,898,989,1082,1183,1278,1378,1471,1566,1662,1753,1843,1932,2042,2146,2252,2363,2465,2583,2746,2852,2942"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,433,543,650,736,840,960,1039,1120,1211,1304,1405,1500,1600,1693,1788,1884,1975,2065,2154,2264,2368,2474,2585,2687,2805,2968,12360", "endColumns": "110,109,106,85,103,119,78,80,90,92,100,94,99,92,94,95,90,89,88,109,103,105,110,101,117,162,105,89", "endOffsets": "428,538,645,731,835,955,1034,1115,1206,1299,1400,1495,1595,1688,1783,1879,1970,2060,2149,2259,2363,2469,2580,2682,2800,2963,3069,12445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a8af07a92579850ca0fa9ee28fde6d76\\transformed\\core-1.13.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "40,41,42,43,44,45,46,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3705,3808,3910,4013,4118,4219,4321,12681", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3803,3905,4008,4113,4214,4316,4435,12777"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7979c5b569631585b896f06112c70bce\\transformed\\browser-1.6.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,270,383", "endColumns": "109,104,112,108", "endOffsets": "160,265,378,487"}, "to": {"startLines": "68,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6998,7322,7427,7540", "endColumns": "109,104,112,108", "endOffsets": "7103,7422,7535,7644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\29acdc0321d00a6f785c8c7eac64d289\\transformed\\material-1.12.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,773,878,957,1017,1082,1171,1236,1295,1381,1445,1509,1572,1645,1709,1763,1875,1933,1995,2049,2121,2243,2330,2406,2498,2580,2666,2806,2883,2964,3091,3182,3259,3313,3364,3430,3500,3577,3648,3723,3794,3871,3940,4009,4116,4207,4279,4368,4457,4531,4603,4689,4739,4818,4884,4964,5048,5110,5174,5237,5306,5406,5501,5593,5685,5743,5798,5882,5963,6038", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,93,104,78,59,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,75,91,81,85,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83,80,74,74", "endOffsets": "267,349,427,504,590,674,768,873,952,1012,1077,1166,1231,1290,1376,1440,1504,1567,1640,1704,1758,1870,1928,1990,2044,2116,2238,2325,2401,2493,2575,2661,2801,2878,2959,3086,3177,3254,3308,3359,3425,3495,3572,3643,3718,3789,3866,3935,4004,4111,4202,4274,4363,4452,4526,4598,4684,4734,4813,4879,4959,5043,5105,5169,5232,5301,5401,5496,5588,5680,5738,5793,5877,5958,6033,6108"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3298,3380,3458,3535,3621,4440,4534,4639,7108,7168,7233,7649,7714,7773,7859,7923,7987,8050,8123,8187,8241,8353,8411,8473,8527,8599,8721,8808,8884,8976,9058,9144,9284,9361,9442,9569,9660,9737,9791,9842,9908,9978,10055,10126,10201,10272,10349,10418,10487,10594,10685,10757,10846,10935,11009,11081,11167,11217,11296,11362,11442,11526,11588,11652,11715,11784,11884,11979,12071,12163,12221,12276,12450,12531,12606", "endLines": "5,35,36,37,38,39,47,48,49,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139", "endColumns": "12,81,77,76,85,83,93,104,78,59,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,75,91,81,85,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83,80,74,74", "endOffsets": "317,3375,3453,3530,3616,3700,4529,4634,4713,7163,7228,7317,7709,7768,7854,7918,7982,8045,8118,8182,8236,8348,8406,8468,8522,8594,8716,8803,8879,8971,9053,9139,9279,9356,9437,9564,9655,9732,9786,9837,9903,9973,10050,10121,10196,10267,10344,10413,10482,10589,10680,10752,10841,10930,11004,11076,11162,11212,11291,11357,11437,11521,11583,11647,11710,11779,11879,11974,12066,12158,12216,12271,12355,12526,12601,12676"}}]}]}