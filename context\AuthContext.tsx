// /context/AuthContext.tsx
import type { FirebaseAuthTypes } from "@react-native-firebase/auth";
import React, {
  createContext,
  ReactNode,
  useContext,
  useState,
  useEffect,
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";

interface User {
  id: number;
  name: string;
  phone: string | null;
  role: "admin" | "user";
  email: string | null;
  profile_picture: string | null;
  center?: string;
  address?: string;
}

interface AuthContextType {
  confirmation: FirebaseAuthTypes.ConfirmationResult | null;
  setConfirmation: (
    confirmation: FirebaseAuthTypes.ConfirmationResult | null
  ) => void;
  authToken: string | null;
  user: User | null;
  signIn: (token: string, userData: User) => Promise<void>;
  signOut: () => Promise<void>;
  isLoading: boolean;
  isSessionValidated: boolean; // <-- THE NEW, SMARTER STATE
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [confirmation, setConfirmation] =
    useState<FirebaseAuthTypes.ConfirmationResult | null>(null);
  const [authToken, setAuthToken] = useState<string | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSessionValidated, setIsSessionValidated] = useState(false); // <-- STARTS AS FALSE

  useEffect(() => {
    const validateSession = async () => {
      let storedToken: string | null = null;
      try {
        // Load token and user data from storage immediately.
        // This prevents the "logged-out to logged-in" flicker.
        storedToken = await AsyncStorage.getItem("userToken");
        const storedUserData = await AsyncStorage.getItem("userData");
        if (storedToken && storedUserData) {
          setAuthToken(storedToken);
          setUser(JSON.parse(storedUserData));
        }

        if (!storedToken) {
          // If there's no token, the session is definitively not valid.
          throw new Error("No token found");
        }

        // We have a token, now verify it with the server.
        const response = await fetch(
          "https://api.waleedzaitoun.com/api/auth/me",
          {
            headers: {
              Authorization: `Bearer ${storedToken}`,
              Accept: "application/json",
            },
          }
        );

        if (response.ok) {
          const freshUserData: User = await response.json();
          // Token is valid. Set the fresh user data from the server.
          setUser(freshUserData);
          await AsyncStorage.setItem("userData", JSON.stringify(freshUserData));
        } else {
          // Server rejected the token. Clear the session.
          throw new Error("Token validation failed");
        }
      } catch (error) {
        // Any error in the process means the session is invalid.
        setAuthToken(null);
        setUser(null);
        await AsyncStorage.multiRemove(["userToken", "userData"]);
      } finally {
        // We have now validated the session, whether it succeeded or failed.
        setIsSessionValidated(true);
        setIsLoading(false);
      }
    };

    validateSession();
  }, []);

  const signIn = async (token: string, userData: User) => {
    setAuthToken(token);
    setUser(userData);
    setIsSessionValidated(true); // A new sign-in is always a validated session.
    await AsyncStorage.setItem("userToken", token);
    await AsyncStorage.setItem("userData", JSON.stringify(userData));
  };

  const signOut = async () => {
    setAuthToken(null);
    setUser(null);
    setIsSessionValidated(false); // On sign-out, the session is no longer valid.
    await AsyncStorage.multiRemove(["userToken", "userData"]);
  };

  const value = {
    confirmation,
    setConfirmation,
    authToken,
    user,
    signIn,
    signOut,
    isLoading,
    isSessionValidated,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};