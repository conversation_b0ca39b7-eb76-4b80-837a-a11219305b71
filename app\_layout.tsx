// /app/_layout.tsx
import { AuthProvider, useAuth } from '@/context/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { SplashScreen, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import { View } from 'react-native';
import Toast, { BaseToast, ErrorToast } from 'react-native-toast-message';
import 'react-native-reanimated';

// --- NEW: Define the custom toast configuration ---
const toastConfig = {
  // Success Toast with your app's branding
  success: (props: any) => (
    <BaseToast
      {...props}
      style={{ borderLeftColor: '#007BFF', backgroundColor: '#1E232C', height: 80, marginTop: 20, }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
      text1Style={{

        fontSize: 17,
        fontFamily: 'GE_SS_Two_Bold',
        color: '#FFFFFF',
        textAlign: 'right',
        lineHeight: 20,
        writingDirection: 'rtl',
        paddingTop: 10,

      }}
      text2Style={{
        fontSize: 15,
        fontFamily: 'GESSTwo',
        color: '#ECEDEE',
        textAlign: 'right',
      }}
    />
  ),
  // Error Toast with your app's branding
  error: (props: any) => (
    <ErrorToast
      {...props}
      style={{ borderLeftColor: '#FF6347', backgroundColor: '#1E232C', height: 80, marginTop: 20, }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
      text1Style={{
        fontSize: 17,
        fontFamily: 'GE_SS_Two_Bold',
        color: '#FFFFFF',
        textAlign: 'right',
        lineHeight: 20,
        writingDirection: 'rtl',
        paddingTop: 10,
      }}
      text2Style={{
        fontSize: 15,
        fontFamily: 'GESSTwo',
        color: '#ECEDEE',
        textAlign: 'right',
      }}
    />
  ),
};

// --- (The rest of the file is mostly the same) ---

SplashScreen.preventAutoHideAsync();

function RootLayoutNav() {
  const colorScheme = useColorScheme();
  const { isLoading: isAuthLoading } = useAuth();

  const [fontsLoaded, fontError] = useFonts({
    // It is highly recommended to remove any fonts that are not actively used
    // to reduce the initial load time and app size.
    GESSTwo: require('../assets/fonts/GESSTwo.ttf'),
    GESSTwoLight: require('../assets/fonts/GESSTwoLight.ttf'),
    GE_SS_Two_Bold: require('../assets/fonts/GE-SS-Two-Bold.otf'),
    GE_SS_Text_Medium: require('../assets/fonts/GE_SS_Text_Medium.otf'),
    rabi3: require('../assets/fonts/rabi3.ttf'),
    RubikLight: require('../assets/fonts/Rubik-Light.ttf'),
    RubikBold: require('../assets/fonts/Rubik-Bold.ttf'),
  });

  // This effect will hide the splash screen once fonts are loaded AND
  // the auth session has been checked.
  useEffect(() => {
    if (fontError) throw fontError;

    if (fontsLoaded && !isAuthLoading) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError, isAuthLoading]);

  // If fonts haven't loaded or the auth check is running, render nothing.
  // The native splash screen will remain visible.
  if (!fontsLoaded || isAuthLoading) {
    return null;
  }

  // Once everything is loaded, render the app navigator.
  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack>
        <Stack.Screen name="(drawer)" options={{ headerShown: false }} />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
    </ThemeProvider>
  );
}

export default function RootLayout() {
  return (
    <AuthProvider>
      <RootLayoutNav />
      {/* --- ADD THIS LINE: The Toast component needs to be rendered at the root --- */}
      <Toast config={toastConfig} />
    </AuthProvider>
  );
}