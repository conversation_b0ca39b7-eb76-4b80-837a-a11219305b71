import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import React, { memo } from 'react';
import { StyleSheet, View } from 'react-native';

import { ThemedText } from './ThemedText';

export const CampaignSection = memo(() => {
  return (
    <View style={styles.container}>
      {/* Top Text Section */}
      <View style={styles.topSection}>
        <ThemedText style={styles.titleBlue}>الاستحقاق الانتخابي</ThemedText>
        <ThemedText style={styles.titleWhite}>لمجلس الشيوخ ٢٠٢٥</ThemedText>
      </View>

      {/* Bottom Section with Photo and Gradient */}
      <View style={styles.bottomSection}>
        {/* The Blue Gradient Background */}
        <LinearGradient
          colors={['#1971ffff', '#00448d99']}
          style={styles.gradient}
        />

        {/* The main photo, positioned absolutely to overlap */}
        <Image
          // NOTE: Make sure this path is correct for your project
          source={require('@/assets/backgrounds/campaign-background.png')}
          style={styles.personImage}
          cachePolicy="disk"
          contentFit="contain"
        />

        {/* Content on the left of the photo (over the gradient) */}
        <View style={styles.leftColumn}>
          {/* I have removed the logo from here as it belongs in the right column */}
          <Image
            source={require('@/assets/icons/numberOne.png')}
            style={styles.svgIcon}
            cachePolicy="disk"
            contentFit="contain"
          />
          <Image
            source={require('@/assets/icons/penSymbol.png')}
            style={styles.svgIcon}
            cachePolicy="disk"
            contentFit="contain"
          />
          <Image
            source={require('@/assets/icons/campaign-logo.png')}
            style={styles.logoImage}
            cachePolicy="disk"
            contentFit="contain"
          />
        </View>

        {/* Content on the right of the photo (over the photo) */}
        <View style={styles.rightColumn}>
          <ThemedText style={styles.personName}>وليد محمد زيتون</ThemedText>
          <ThemedText style={styles.personTitle}>
            مرشح لانتخابات مجلس الشيوخ ٢٠٢٥
            فردي حزب مستقبل وطن
            عن محافظة كفر الشيخ
          </ThemedText>
          
        </View>
      </View>
    </View>
  );
});
CampaignSection.displayName = "CampaignSection";

const styles = StyleSheet.create({
  container: {},
  topSection: {
    alignItems: 'center',
  },
  titleBlue: {
    fontFamily: 'GESSTwoLight',
    color: '#007BFF',
    fontSize: 28,
  },
  titleWhite: {
    fontFamily: 'RubikBold',
    color: '#FFFFFF',
    fontSize: 30,
    lineHeight: 56,
    marginTop: 10,
  },
  bottomSection: {
    paddingTop: 60,
    marginTop: 100,
    height: 400, // Adjust height as needed
    position: 'relative',
    flexDirection: 'row',
  },
  gradient: {
    ...StyleSheet.absoluteFillObject,
  },
  personImage: {
    position: 'absolute',
    bottom: -75,
    // Adjust right positioning to make it appear centered in the right half
    right: -100,
    // Adjust width and height to make the image appear larger
    width: '120%',
    height: '170%',
  },
  leftColumn: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 25,
    paddingRight: 110, // Pushes content away from the center
    paddingBottom: 70, // Pushes content away from the center
  },
  // FIX: This style correctly aligns all content to the bottom-right corner.
  rightColumn: {
    flex: 1,
    justifyContent: 'flex-end', // Pushes content to the bottom
    alignItems: 'flex-end',   // Pushes content to the right
    marginEnd: 1,
    paddingTop: 200,
  },
  svgIcon: {
    width: 110,
    height: 110,
  },
  personName: {
    fontFamily: 'RubikBold',
    fontSize: 24,
    color: 'white',
    textAlign: 'center',
    width: 280,
    lineHeight: 30,

  },
  personTitle: {
    fontFamily: 'GE_SS_Text_Medium',
    fontSize: 12,
    color: 'white',
    textAlign: 'center',
    lineHeight: 18,
    width: 280,
  },
  logoImage: {
    width: 80,
    height: 80,
    marginTop: 20,
  },
});