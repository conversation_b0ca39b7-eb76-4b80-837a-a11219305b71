{"name": "<PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "<PERSON>ayton", "userInterfaceStyle": "automatic", "newArchEnabled": true, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.omaraglan96.zayton", "googleServicesFile": "./google-services.json", "edgeToEdgeEnabled": true}, "ios": {"supportsTablet": true, "googleServicesFile": "./GoogleService-Info.plist", "bundleIdentifier": "com.mycorp.myapp"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["@react-native-firebase/app", "@react-native-firebase/auth", "expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "8412dabd-5640-4532-a6d8-96db1868e6d7"}}, "owner": "omaraglan96", "sdkVersion": "53.0.0", "platforms": ["ios", "android", "web"], "androidStatusBar": {"backgroundColor": "#ffffff"}}