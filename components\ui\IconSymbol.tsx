// Fallback for using MaterialIcons on Android and web.

import { Ionicons } from '@expo/vector-icons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { SymbolViewProps, SymbolWeight } from 'expo-symbols';
import { ComponentProps } from 'react';
import { OpaqueColorValue, type StyleProp, type TextStyle } from 'react-native';

type MaterialIconMapping = Partial<Record<SymbolViewProps['name'], ComponentProps<typeof MaterialIcons>['name']>>;
type IoniconsMapping = Partial<Record<SymbolViewProps['name'], ComponentProps<typeof Ionicons>['name']>>;
type IconSymbolName = keyof typeof MATERIAL_MAPPING | keyof typeof IONICONS_MAPPING;

/**
 * Add your SF Symbols to Material Icons mappings here.
 */
const MATERIAL_MAPPING = {
  'house.fill': 'home',
  'paperplane.fill': 'send',
  'chevron.left.forwardslash.chevron.right': 'code',
  'chevron.right': 'chevron-right',
  // ADDED: Mappings for news and register icons
  newspaper: 'article',
  person: 'person',
  homenew: 'home-outline',
} as MaterialIconMapping;

/**
 * Add your SF Symbols to Ionicons mappings here.
 */
const IONICONS_MAPPING = {
  'house.fill': 'home',
  'paperplane.fill': 'send',
  'chevron.right': 'chevron-forward',
  'document-text': 'document-text',
  
  newspaper: 'newspaper',
  person: 'person',
  menu: 'menu',
  close: 'close',
  search: 'search',
  settings: 'settings',
  notification: 'notifications',
  
} as IoniconsMapping;

/**
 * An icon component that uses native SF Symbols on iOS, and Ionicons or Material Icons on Android and web.
 */
export function IconSymbol({
  name,
  size = 24,
  color,
  style,
}: {
  name: IconSymbolName;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<TextStyle>;
  weight?: SymbolWeight;
}) {
  // Check if the icon exists in Ionicons mapping first, then fallback to Material Icons
  if (IONICONS_MAPPING[name]) {
    return <Ionicons color={color} size={size} name={IONICONS_MAPPING[name]} style={style} />;
  } else if (MATERIAL_MAPPING[name]) {
    return <MaterialIcons color={color} size={size} name={MATERIAL_MAPPING[name]} style={style} />;
  }
  
  // Fallback to Material Icons if not found in either mapping
  return <MaterialIcons color={color} size={size} name="help" style={style} />;
}